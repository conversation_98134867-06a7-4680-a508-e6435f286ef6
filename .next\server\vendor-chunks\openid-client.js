/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/openid-client";
exports.ids = ["vendor-chunks/openid-client"];
exports.modules = {

/***/ "(rsc)/./node_modules/openid-client/lib/client.js":
/*!**************************************************!*\
  !*** ./node_modules/openid-client/lib/client.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { inspect } = __webpack_require__(/*! util */ \"util\");\nconst stdhttp = __webpack_require__(/*! http */ \"http\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nconst { strict: assert } = __webpack_require__(/*! assert */ \"assert\");\nconst querystring = __webpack_require__(/*! querystring */ \"querystring\");\nconst url = __webpack_require__(/*! url */ \"url\");\nconst { URL, URLSearchParams } = __webpack_require__(/*! url */ \"url\");\nconst jose = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/cjs/index.js\");\nconst tokenHash = __webpack_require__(/*! oidc-token-hash */ \"(rsc)/./node_modules/oidc-token-hash/lib/index.js\");\nconst isKeyObject = __webpack_require__(/*! ./helpers/is_key_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_key_object.js\");\nconst decodeJWT = __webpack_require__(/*! ./helpers/decode_jwt */ \"(rsc)/./node_modules/openid-client/lib/helpers/decode_jwt.js\");\nconst base64url = __webpack_require__(/*! ./helpers/base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\nconst defaults = __webpack_require__(/*! ./helpers/defaults */ \"(rsc)/./node_modules/openid-client/lib/helpers/defaults.js\");\nconst parseWwwAuthenticate = __webpack_require__(/*! ./helpers/www_authenticate_parser */ \"(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js\");\nconst { assertSigningAlgValuesSupport, assertIssuerConfiguration } = __webpack_require__(/*! ./helpers/assert */ \"(rsc)/./node_modules/openid-client/lib/helpers/assert.js\");\nconst pick = __webpack_require__(/*! ./helpers/pick */ \"(rsc)/./node_modules/openid-client/lib/helpers/pick.js\");\nconst isPlainObject = __webpack_require__(/*! ./helpers/is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\nconst processResponse = __webpack_require__(/*! ./helpers/process_response */ \"(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\");\nconst TokenSet = __webpack_require__(/*! ./token_set */ \"(rsc)/./node_modules/openid-client/lib/token_set.js\");\nconst { OPError, RPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst now = __webpack_require__(/*! ./helpers/unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\nconst { random } = __webpack_require__(/*! ./helpers/generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst request = __webpack_require__(/*! ./helpers/request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nconst { CLOCK_TOLERANCE } = __webpack_require__(/*! ./helpers/consts */ \"(rsc)/./node_modules/openid-client/lib/helpers/consts.js\");\nconst { keystores } = __webpack_require__(/*! ./helpers/weak_cache */ \"(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\");\nconst KeyStore = __webpack_require__(/*! ./helpers/keystore */ \"(rsc)/./node_modules/openid-client/lib/helpers/keystore.js\");\nconst clone = __webpack_require__(/*! ./helpers/deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst { authenticatedPost, resolveResponseType, resolveRedirectUri } = __webpack_require__(/*! ./helpers/client */ \"(rsc)/./node_modules/openid-client/lib/helpers/client.js\");\nconst { queryKeyStore } = __webpack_require__(/*! ./helpers/issuer */ \"(rsc)/./node_modules/openid-client/lib/helpers/issuer.js\");\nconst DeviceFlowHandle = __webpack_require__(/*! ./device_flow_handle */ \"(rsc)/./node_modules/openid-client/lib/device_flow_handle.js\");\nconst [major, minor] = process.version.slice(1).split(\".\").map((str)=>parseInt(str, 10));\nconst rsaPssParams = major >= 17 || major === 16 && minor >= 9;\nconst retryAttempt = Symbol();\nconst skipNonceCheck = Symbol();\nconst skipMaxAgeCheck = Symbol();\nfunction pickCb(input) {\n    return pick(input, \"access_token\", \"code\", \"error_description\", \"error_uri\", \"error\", \"expires_in\", \"id_token\", \"iss\", \"response\", \"session_state\", \"state\", \"token_type\");\n}\nfunction authorizationHeaderValue(token, tokenType = \"Bearer\") {\n    return `${tokenType} ${token}`;\n}\nfunction getSearchParams(input) {\n    const parsed = url.parse(input);\n    if (!parsed.search) return {};\n    return querystring.parse(parsed.search.substring(1));\n}\nfunction verifyPresence(payload, jwt, prop) {\n    if (payload[prop] === undefined) {\n        throw new RPError({\n            message: `missing required JWT property ${prop}`,\n            jwt\n        });\n    }\n}\nfunction authorizationParams(params) {\n    const authParams = {\n        client_id: this.client_id,\n        scope: \"openid\",\n        response_type: resolveResponseType.call(this),\n        redirect_uri: resolveRedirectUri.call(this),\n        ...params\n    };\n    Object.entries(authParams).forEach(([key, value])=>{\n        if (value === null || value === undefined) {\n            delete authParams[key];\n        } else if (key === \"claims\" && typeof value === \"object\") {\n            authParams[key] = JSON.stringify(value);\n        } else if (key === \"resource\" && Array.isArray(value)) {\n            authParams[key] = value;\n        } else if (typeof value !== \"string\") {\n            authParams[key] = String(value);\n        }\n    });\n    return authParams;\n}\nfunction getKeystore(jwks) {\n    if (!isPlainObject(jwks) || !Array.isArray(jwks.keys) || jwks.keys.some((k)=>!isPlainObject(k) || !(\"kty\" in k))) {\n        throw new TypeError(\"jwks must be a JSON Web Key Set formatted object\");\n    }\n    return KeyStore.fromJWKS(jwks, {\n        onlyPrivate: true\n    });\n}\n// if an OP doesnt support client_secret_basic but supports client_secret_post, use it instead\n// this is in place to take care of most common pitfalls when first using discovered Issuers without\n// the support for default values defined by Discovery 1.0\nfunction checkBasicSupport(client, properties) {\n    try {\n        const supported = client.issuer.token_endpoint_auth_methods_supported;\n        if (!supported.includes(properties.token_endpoint_auth_method)) {\n            if (supported.includes(\"client_secret_post\")) {\n                properties.token_endpoint_auth_method = \"client_secret_post\";\n            }\n        }\n    } catch (err) {}\n}\nfunction handleCommonMistakes(client, metadata, properties) {\n    if (!metadata.token_endpoint_auth_method) {\n        // if no explicit value was provided\n        checkBasicSupport(client, properties);\n    }\n    // :fp: c'mon people... RTFM\n    if (metadata.redirect_uri) {\n        if (metadata.redirect_uris) {\n            throw new TypeError(\"provide a redirect_uri or redirect_uris, not both\");\n        }\n        properties.redirect_uris = [\n            metadata.redirect_uri\n        ];\n        delete properties.redirect_uri;\n    }\n    if (metadata.response_type) {\n        if (metadata.response_types) {\n            throw new TypeError(\"provide a response_type or response_types, not both\");\n        }\n        properties.response_types = [\n            metadata.response_type\n        ];\n        delete properties.response_type;\n    }\n}\nfunction getDefaultsForEndpoint(endpoint, issuer, properties) {\n    if (!issuer[`${endpoint}_endpoint`]) return;\n    const tokenEndpointAuthMethod = properties.token_endpoint_auth_method;\n    const tokenEndpointAuthSigningAlg = properties.token_endpoint_auth_signing_alg;\n    const eam = `${endpoint}_endpoint_auth_method`;\n    const easa = `${endpoint}_endpoint_auth_signing_alg`;\n    if (properties[eam] === undefined && properties[easa] === undefined) {\n        if (tokenEndpointAuthMethod !== undefined) {\n            properties[eam] = tokenEndpointAuthMethod;\n        }\n        if (tokenEndpointAuthSigningAlg !== undefined) {\n            properties[easa] = tokenEndpointAuthSigningAlg;\n        }\n    }\n}\nclass BaseClient {\n    #metadata;\n    #issuer;\n    #aadIssValidation;\n    #additionalAuthorizedParties;\n    constructor(issuer, aadIssValidation, metadata = {}, jwks, options){\n        this.#metadata = new Map();\n        this.#issuer = issuer;\n        this.#aadIssValidation = aadIssValidation;\n        if (typeof metadata.client_id !== \"string\" || !metadata.client_id) {\n            throw new TypeError(\"client_id is required\");\n        }\n        const properties = {\n            grant_types: [\n                \"authorization_code\"\n            ],\n            id_token_signed_response_alg: \"RS256\",\n            authorization_signed_response_alg: \"RS256\",\n            response_types: [\n                \"code\"\n            ],\n            token_endpoint_auth_method: \"client_secret_basic\",\n            ...this.fapi1() ? {\n                grant_types: [\n                    \"authorization_code\",\n                    \"implicit\"\n                ],\n                id_token_signed_response_alg: \"PS256\",\n                authorization_signed_response_alg: \"PS256\",\n                response_types: [\n                    \"code id_token\"\n                ],\n                tls_client_certificate_bound_access_tokens: true,\n                token_endpoint_auth_method: undefined\n            } : undefined,\n            ...this.fapi2() ? {\n                id_token_signed_response_alg: \"PS256\",\n                authorization_signed_response_alg: \"PS256\",\n                token_endpoint_auth_method: undefined\n            } : undefined,\n            ...metadata\n        };\n        if (this.fapi()) {\n            switch(properties.token_endpoint_auth_method){\n                case \"self_signed_tls_client_auth\":\n                case \"tls_client_auth\":\n                    break;\n                case \"private_key_jwt\":\n                    if (!jwks) {\n                        throw new TypeError(\"jwks is required\");\n                    }\n                    break;\n                case undefined:\n                    throw new TypeError(\"token_endpoint_auth_method is required\");\n                default:\n                    throw new TypeError(\"invalid or unsupported token_endpoint_auth_method\");\n            }\n        }\n        if (this.fapi2()) {\n            if (properties.tls_client_certificate_bound_access_tokens && properties.dpop_bound_access_tokens) {\n                throw new TypeError(\"either tls_client_certificate_bound_access_tokens or dpop_bound_access_tokens must be set to true\");\n            }\n            if (!properties.tls_client_certificate_bound_access_tokens && !properties.dpop_bound_access_tokens) {\n                throw new TypeError(\"either tls_client_certificate_bound_access_tokens or dpop_bound_access_tokens must be set to true\");\n            }\n        }\n        handleCommonMistakes(this, metadata, properties);\n        assertSigningAlgValuesSupport(\"token\", this.issuer, properties);\n        [\n            \"introspection\",\n            \"revocation\"\n        ].forEach((endpoint)=>{\n            getDefaultsForEndpoint(endpoint, this.issuer, properties);\n            assertSigningAlgValuesSupport(endpoint, this.issuer, properties);\n        });\n        Object.entries(properties).forEach(([key, value])=>{\n            this.#metadata.set(key, value);\n            if (!this[key]) {\n                Object.defineProperty(this, key, {\n                    get () {\n                        return this.#metadata.get(key);\n                    },\n                    enumerable: true\n                });\n            }\n        });\n        if (jwks !== undefined) {\n            const keystore = getKeystore.call(this, jwks);\n            keystores.set(this, keystore);\n        }\n        if (options != null && options.additionalAuthorizedParties) {\n            this.#additionalAuthorizedParties = clone(options.additionalAuthorizedParties);\n        }\n        this[CLOCK_TOLERANCE] = 0;\n    }\n    authorizationUrl(params = {}) {\n        if (!isPlainObject(params)) {\n            throw new TypeError(\"params must be a plain object\");\n        }\n        assertIssuerConfiguration(this.issuer, \"authorization_endpoint\");\n        const target = new URL(this.issuer.authorization_endpoint);\n        for (const [name, value] of Object.entries(authorizationParams.call(this, params))){\n            if (Array.isArray(value)) {\n                target.searchParams.delete(name);\n                for (const member of value){\n                    target.searchParams.append(name, member);\n                }\n            } else {\n                target.searchParams.set(name, value);\n            }\n        }\n        // TODO: is the replace needed?\n        return target.href.replace(/\\+/g, \"%20\");\n    }\n    authorizationPost(params = {}) {\n        if (!isPlainObject(params)) {\n            throw new TypeError(\"params must be a plain object\");\n        }\n        const inputs = authorizationParams.call(this, params);\n        const formInputs = Object.keys(inputs).map((name)=>`<input type=\"hidden\" name=\"${name}\" value=\"${inputs[name]}\"/>`).join(\"\\n\");\n        return `<!DOCTYPE html>\n<head>\n<title>Requesting Authorization</title>\n</head>\n<body onload=\"javascript:document.forms[0].submit()\">\n<form method=\"post\" action=\"${this.issuer.authorization_endpoint}\">\n  ${formInputs}\n</form>\n</body>\n</html>`;\n    }\n    endSessionUrl(params = {}) {\n        assertIssuerConfiguration(this.issuer, \"end_session_endpoint\");\n        const { 0: postLogout, length } = this.post_logout_redirect_uris || [];\n        const { post_logout_redirect_uri = length === 1 ? postLogout : undefined } = params;\n        let id_token_hint;\n        ({ id_token_hint, ...params } = params);\n        if (id_token_hint instanceof TokenSet) {\n            if (!id_token_hint.id_token) {\n                throw new TypeError(\"id_token not present in TokenSet\");\n            }\n            id_token_hint = id_token_hint.id_token;\n        }\n        const target = url.parse(this.issuer.end_session_endpoint);\n        const query = defaults(getSearchParams(this.issuer.end_session_endpoint), params, {\n            post_logout_redirect_uri,\n            client_id: this.client_id\n        }, {\n            id_token_hint\n        });\n        Object.entries(query).forEach(([key, value])=>{\n            if (value === null || value === undefined) {\n                delete query[key];\n            }\n        });\n        target.search = null;\n        target.query = query;\n        return url.format(target);\n    }\n    callbackParams(input) {\n        const isIncomingMessage = input instanceof stdhttp.IncomingMessage || input && input.method && input.url;\n        const isString = typeof input === \"string\";\n        if (!isString && !isIncomingMessage) {\n            throw new TypeError(\"#callbackParams only accepts string urls, http.IncomingMessage or a lookalike\");\n        }\n        if (isIncomingMessage) {\n            switch(input.method){\n                case \"GET\":\n                    return pickCb(getSearchParams(input.url));\n                case \"POST\":\n                    if (input.body === undefined) {\n                        throw new TypeError(\"incoming message body missing, include a body parser prior to this method call\");\n                    }\n                    switch(typeof input.body){\n                        case \"object\":\n                        case \"string\":\n                            if (Buffer.isBuffer(input.body)) {\n                                return pickCb(querystring.parse(input.body.toString(\"utf-8\")));\n                            }\n                            if (typeof input.body === \"string\") {\n                                return pickCb(querystring.parse(input.body));\n                            }\n                            return pickCb(input.body);\n                        default:\n                            throw new TypeError(\"invalid IncomingMessage body object\");\n                    }\n                default:\n                    throw new TypeError(\"invalid IncomingMessage method\");\n            }\n        } else {\n            return pickCb(getSearchParams(input));\n        }\n    }\n    async callback(redirectUri, parameters, checks = {}, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        let params = pickCb(parameters);\n        if (checks.jarm && !(\"response\" in parameters)) {\n            throw new RPError({\n                message: \"expected a JARM response\",\n                checks,\n                params\n            });\n        } else if (\"response\" in parameters) {\n            const decrypted = await this.decryptJARM(params.response);\n            params = await this.validateJARM(decrypted);\n        }\n        if (this.default_max_age && !checks.max_age) {\n            checks.max_age = this.default_max_age;\n        }\n        if (params.state && !checks.state) {\n            throw new TypeError(\"checks.state argument is missing\");\n        }\n        if (!params.state && checks.state) {\n            throw new RPError({\n                message: \"state missing from the response\",\n                checks,\n                params\n            });\n        }\n        if (checks.state !== params.state) {\n            throw new RPError({\n                printf: [\n                    \"state mismatch, expected %s, got: %s\",\n                    checks.state,\n                    params.state\n                ],\n                checks,\n                params\n            });\n        }\n        if (\"iss\" in params) {\n            assertIssuerConfiguration(this.issuer, \"issuer\");\n            if (params.iss !== this.issuer.issuer) {\n                throw new RPError({\n                    printf: [\n                        \"iss mismatch, expected %s, got: %s\",\n                        this.issuer.issuer,\n                        params.iss\n                    ],\n                    params\n                });\n            }\n        } else if (this.issuer.authorization_response_iss_parameter_supported && !(\"id_token\" in params) && !(\"response\" in parameters)) {\n            throw new RPError({\n                message: \"iss missing from the response\",\n                params\n            });\n        }\n        if (params.error) {\n            throw new OPError(params);\n        }\n        const RESPONSE_TYPE_REQUIRED_PARAMS = {\n            code: [\n                \"code\"\n            ],\n            id_token: [\n                \"id_token\"\n            ],\n            token: [\n                \"access_token\",\n                \"token_type\"\n            ]\n        };\n        if (checks.response_type) {\n            for (const type of checks.response_type.split(\" \")){\n                if (type === \"none\") {\n                    if (params.code || params.id_token || params.access_token) {\n                        throw new RPError({\n                            message: 'unexpected params encountered for \"none\" response',\n                            checks,\n                            params\n                        });\n                    }\n                } else {\n                    for (const param of RESPONSE_TYPE_REQUIRED_PARAMS[type]){\n                        if (!params[param]) {\n                            throw new RPError({\n                                message: `${param} missing from response`,\n                                checks,\n                                params\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        if (params.id_token) {\n            const tokenset = new TokenSet(params);\n            await this.decryptIdToken(tokenset);\n            await this.validateIdToken(tokenset, checks.nonce, \"authorization\", checks.max_age, checks.state);\n            if (!params.code) {\n                return tokenset;\n            }\n        }\n        if (params.code) {\n            const tokenset = await this.grant({\n                ...exchangeBody,\n                grant_type: \"authorization_code\",\n                code: params.code,\n                redirect_uri: redirectUri,\n                code_verifier: checks.code_verifier\n            }, {\n                clientAssertionPayload,\n                DPoP\n            });\n            await this.decryptIdToken(tokenset);\n            await this.validateIdToken(tokenset, checks.nonce, \"token\", checks.max_age);\n            if (params.session_state) {\n                tokenset.session_state = params.session_state;\n            }\n            return tokenset;\n        }\n        return new TokenSet(params);\n    }\n    async oauthCallback(redirectUri, parameters, checks = {}, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        let params = pickCb(parameters);\n        if (checks.jarm && !(\"response\" in parameters)) {\n            throw new RPError({\n                message: \"expected a JARM response\",\n                checks,\n                params\n            });\n        } else if (\"response\" in parameters) {\n            const decrypted = await this.decryptJARM(params.response);\n            params = await this.validateJARM(decrypted);\n        }\n        if (params.state && !checks.state) {\n            throw new TypeError(\"checks.state argument is missing\");\n        }\n        if (!params.state && checks.state) {\n            throw new RPError({\n                message: \"state missing from the response\",\n                checks,\n                params\n            });\n        }\n        if (checks.state !== params.state) {\n            throw new RPError({\n                printf: [\n                    \"state mismatch, expected %s, got: %s\",\n                    checks.state,\n                    params.state\n                ],\n                checks,\n                params\n            });\n        }\n        if (\"iss\" in params) {\n            assertIssuerConfiguration(this.issuer, \"issuer\");\n            if (params.iss !== this.issuer.issuer) {\n                throw new RPError({\n                    printf: [\n                        \"iss mismatch, expected %s, got: %s\",\n                        this.issuer.issuer,\n                        params.iss\n                    ],\n                    params\n                });\n            }\n        } else if (this.issuer.authorization_response_iss_parameter_supported && !(\"id_token\" in params) && !(\"response\" in parameters)) {\n            throw new RPError({\n                message: \"iss missing from the response\",\n                params\n            });\n        }\n        if (params.error) {\n            throw new OPError(params);\n        }\n        if (typeof params.id_token === \"string\" && params.id_token.length) {\n            throw new RPError({\n                message: \"id_token detected in the response, you must use client.callback() instead of client.oauthCallback()\",\n                params\n            });\n        }\n        delete params.id_token;\n        const RESPONSE_TYPE_REQUIRED_PARAMS = {\n            code: [\n                \"code\"\n            ],\n            token: [\n                \"access_token\",\n                \"token_type\"\n            ]\n        };\n        if (checks.response_type) {\n            for (const type of checks.response_type.split(\" \")){\n                if (type === \"none\") {\n                    if (params.code || params.id_token || params.access_token) {\n                        throw new RPError({\n                            message: 'unexpected params encountered for \"none\" response',\n                            checks,\n                            params\n                        });\n                    }\n                }\n                if (RESPONSE_TYPE_REQUIRED_PARAMS[type]) {\n                    for (const param of RESPONSE_TYPE_REQUIRED_PARAMS[type]){\n                        if (!params[param]) {\n                            throw new RPError({\n                                message: `${param} missing from response`,\n                                checks,\n                                params\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        if (params.code) {\n            const tokenset = await this.grant({\n                ...exchangeBody,\n                grant_type: \"authorization_code\",\n                code: params.code,\n                redirect_uri: redirectUri,\n                code_verifier: checks.code_verifier\n            }, {\n                clientAssertionPayload,\n                DPoP\n            });\n            if (typeof tokenset.id_token === \"string\" && tokenset.id_token.length) {\n                throw new RPError({\n                    message: \"id_token detected in the response, you must use client.callback() instead of client.oauthCallback()\",\n                    params\n                });\n            }\n            delete tokenset.id_token;\n            return tokenset;\n        }\n        return new TokenSet(params);\n    }\n    async decryptIdToken(token) {\n        if (!this.id_token_encrypted_response_alg) {\n            return token;\n        }\n        let idToken = token;\n        if (idToken instanceof TokenSet) {\n            if (!idToken.id_token) {\n                throw new TypeError(\"id_token not present in TokenSet\");\n            }\n            idToken = idToken.id_token;\n        }\n        const expectedAlg = this.id_token_encrypted_response_alg;\n        const expectedEnc = this.id_token_encrypted_response_enc;\n        const result = await this.decryptJWE(idToken, expectedAlg, expectedEnc);\n        if (token instanceof TokenSet) {\n            token.id_token = result;\n            return token;\n        }\n        return result;\n    }\n    async validateJWTUserinfo(body) {\n        const expectedAlg = this.userinfo_signed_response_alg;\n        return this.validateJWT(body, expectedAlg, []);\n    }\n    async decryptJARM(response) {\n        if (!this.authorization_encrypted_response_alg) {\n            return response;\n        }\n        const expectedAlg = this.authorization_encrypted_response_alg;\n        const expectedEnc = this.authorization_encrypted_response_enc;\n        return this.decryptJWE(response, expectedAlg, expectedEnc);\n    }\n    async decryptJWTUserinfo(body) {\n        if (!this.userinfo_encrypted_response_alg) {\n            return body;\n        }\n        const expectedAlg = this.userinfo_encrypted_response_alg;\n        const expectedEnc = this.userinfo_encrypted_response_enc;\n        return this.decryptJWE(body, expectedAlg, expectedEnc);\n    }\n    async decryptJWE(jwe, expectedAlg, expectedEnc = \"A128CBC-HS256\") {\n        const header = JSON.parse(base64url.decode(jwe.split(\".\")[0]));\n        if (header.alg !== expectedAlg) {\n            throw new RPError({\n                printf: [\n                    \"unexpected JWE alg received, expected %s, got: %s\",\n                    expectedAlg,\n                    header.alg\n                ],\n                jwt: jwe\n            });\n        }\n        if (header.enc !== expectedEnc) {\n            throw new RPError({\n                printf: [\n                    \"unexpected JWE enc received, expected %s, got: %s\",\n                    expectedEnc,\n                    header.enc\n                ],\n                jwt: jwe\n            });\n        }\n        const getPlaintext = (result)=>new TextDecoder().decode(result.plaintext);\n        let plaintext;\n        if (expectedAlg.match(/^(?:RSA|ECDH)/)) {\n            const keystore = await keystores.get(this);\n            const protectedHeader = jose.decodeProtectedHeader(jwe);\n            for (const key of keystore.all({\n                ...protectedHeader,\n                use: \"enc\"\n            })){\n                plaintext = await jose.compactDecrypt(jwe, await key.keyObject(protectedHeader.alg)).then(getPlaintext, ()=>{});\n                if (plaintext) break;\n            }\n        } else {\n            plaintext = await jose.compactDecrypt(jwe, this.secretForAlg(expectedAlg === \"dir\" ? expectedEnc : expectedAlg)).then(getPlaintext, ()=>{});\n        }\n        if (!plaintext) {\n            throw new RPError({\n                message: \"failed to decrypt JWE\",\n                jwt: jwe\n            });\n        }\n        return plaintext;\n    }\n    async validateIdToken(tokenSet, nonce, returnedBy, maxAge, state) {\n        let idToken = tokenSet;\n        const expectedAlg = this.id_token_signed_response_alg;\n        const isTokenSet = idToken instanceof TokenSet;\n        if (isTokenSet) {\n            if (!idToken.id_token) {\n                throw new TypeError(\"id_token not present in TokenSet\");\n            }\n            idToken = idToken.id_token;\n        }\n        idToken = String(idToken);\n        const timestamp = now();\n        const { protected: header, payload, key } = await this.validateJWT(idToken, expectedAlg);\n        if (typeof maxAge === \"number\" || maxAge !== skipMaxAgeCheck && this.require_auth_time) {\n            if (!payload.auth_time) {\n                throw new RPError({\n                    message: \"missing required JWT property auth_time\",\n                    jwt: idToken\n                });\n            }\n            if (typeof payload.auth_time !== \"number\") {\n                throw new RPError({\n                    message: \"JWT auth_time claim must be a JSON numeric value\",\n                    jwt: idToken\n                });\n            }\n        }\n        if (typeof maxAge === \"number\" && payload.auth_time + maxAge < timestamp - this[CLOCK_TOLERANCE]) {\n            throw new RPError({\n                printf: [\n                    \"too much time has elapsed since the last End-User authentication, max_age %i, auth_time: %i, now %i\",\n                    maxAge,\n                    payload.auth_time,\n                    timestamp - this[CLOCK_TOLERANCE]\n                ],\n                now: timestamp,\n                tolerance: this[CLOCK_TOLERANCE],\n                auth_time: payload.auth_time,\n                jwt: idToken\n            });\n        }\n        if (nonce !== skipNonceCheck && (payload.nonce || nonce !== undefined) && payload.nonce !== nonce) {\n            throw new RPError({\n                printf: [\n                    \"nonce mismatch, expected %s, got: %s\",\n                    nonce,\n                    payload.nonce\n                ],\n                jwt: idToken\n            });\n        }\n        if (returnedBy === \"authorization\") {\n            if (!payload.at_hash && tokenSet.access_token) {\n                throw new RPError({\n                    message: \"missing required property at_hash\",\n                    jwt: idToken\n                });\n            }\n            if (!payload.c_hash && tokenSet.code) {\n                throw new RPError({\n                    message: \"missing required property c_hash\",\n                    jwt: idToken\n                });\n            }\n            if (this.fapi1()) {\n                if (!payload.s_hash && (tokenSet.state || state)) {\n                    throw new RPError({\n                        message: \"missing required property s_hash\",\n                        jwt: idToken\n                    });\n                }\n            }\n            if (payload.s_hash) {\n                if (!state) {\n                    throw new TypeError('cannot verify s_hash, \"checks.state\" property not provided');\n                }\n                try {\n                    tokenHash.validate({\n                        claim: \"s_hash\",\n                        source: \"state\"\n                    }, payload.s_hash, state, header.alg, key.jwk && key.jwk.crv);\n                } catch (err) {\n                    throw new RPError({\n                        message: err.message,\n                        jwt: idToken\n                    });\n                }\n            }\n        }\n        if (this.fapi() && payload.iat < timestamp - 3600) {\n            throw new RPError({\n                printf: [\n                    \"JWT issued too far in the past, now %i, iat %i\",\n                    timestamp,\n                    payload.iat\n                ],\n                now: timestamp,\n                tolerance: this[CLOCK_TOLERANCE],\n                iat: payload.iat,\n                jwt: idToken\n            });\n        }\n        if (tokenSet.access_token && payload.at_hash !== undefined) {\n            try {\n                tokenHash.validate({\n                    claim: \"at_hash\",\n                    source: \"access_token\"\n                }, payload.at_hash, tokenSet.access_token, header.alg, key.jwk && key.jwk.crv);\n            } catch (err) {\n                throw new RPError({\n                    message: err.message,\n                    jwt: idToken\n                });\n            }\n        }\n        if (tokenSet.code && payload.c_hash !== undefined) {\n            try {\n                tokenHash.validate({\n                    claim: \"c_hash\",\n                    source: \"code\"\n                }, payload.c_hash, tokenSet.code, header.alg, key.jwk && key.jwk.crv);\n            } catch (err) {\n                throw new RPError({\n                    message: err.message,\n                    jwt: idToken\n                });\n            }\n        }\n        return tokenSet;\n    }\n    async validateJWT(jwt, expectedAlg, required = [\n        \"iss\",\n        \"sub\",\n        \"aud\",\n        \"exp\",\n        \"iat\"\n    ]) {\n        const isSelfIssued = this.issuer.issuer === \"https://self-issued.me\";\n        const timestamp = now();\n        let header;\n        let payload;\n        try {\n            ({ header, payload } = decodeJWT(jwt, {\n                complete: true\n            }));\n        } catch (err) {\n            throw new RPError({\n                printf: [\n                    \"failed to decode JWT (%s: %s)\",\n                    err.name,\n                    err.message\n                ],\n                jwt\n            });\n        }\n        if (header.alg !== expectedAlg) {\n            throw new RPError({\n                printf: [\n                    \"unexpected JWT alg received, expected %s, got: %s\",\n                    expectedAlg,\n                    header.alg\n                ],\n                jwt\n            });\n        }\n        if (isSelfIssued) {\n            required = [\n                ...required,\n                \"sub_jwk\"\n            ];\n        }\n        required.forEach(verifyPresence.bind(undefined, payload, jwt));\n        if (payload.iss !== undefined) {\n            let expectedIss = this.issuer.issuer;\n            if (this.#aadIssValidation) {\n                expectedIss = this.issuer.issuer.replace(\"{tenantid}\", payload.tid);\n            }\n            if (payload.iss !== expectedIss) {\n                throw new RPError({\n                    printf: [\n                        \"unexpected iss value, expected %s, got: %s\",\n                        expectedIss,\n                        payload.iss\n                    ],\n                    jwt\n                });\n            }\n        }\n        if (payload.iat !== undefined) {\n            if (typeof payload.iat !== \"number\") {\n                throw new RPError({\n                    message: \"JWT iat claim must be a JSON numeric value\",\n                    jwt\n                });\n            }\n        }\n        if (payload.nbf !== undefined) {\n            if (typeof payload.nbf !== \"number\") {\n                throw new RPError({\n                    message: \"JWT nbf claim must be a JSON numeric value\",\n                    jwt\n                });\n            }\n            if (payload.nbf > timestamp + this[CLOCK_TOLERANCE]) {\n                throw new RPError({\n                    printf: [\n                        \"JWT not active yet, now %i, nbf %i\",\n                        timestamp + this[CLOCK_TOLERANCE],\n                        payload.nbf\n                    ],\n                    now: timestamp,\n                    tolerance: this[CLOCK_TOLERANCE],\n                    nbf: payload.nbf,\n                    jwt\n                });\n            }\n        }\n        if (payload.exp !== undefined) {\n            if (typeof payload.exp !== \"number\") {\n                throw new RPError({\n                    message: \"JWT exp claim must be a JSON numeric value\",\n                    jwt\n                });\n            }\n            if (timestamp - this[CLOCK_TOLERANCE] >= payload.exp) {\n                throw new RPError({\n                    printf: [\n                        \"JWT expired, now %i, exp %i\",\n                        timestamp - this[CLOCK_TOLERANCE],\n                        payload.exp\n                    ],\n                    now: timestamp,\n                    tolerance: this[CLOCK_TOLERANCE],\n                    exp: payload.exp,\n                    jwt\n                });\n            }\n        }\n        if (payload.aud !== undefined) {\n            if (Array.isArray(payload.aud)) {\n                if (payload.aud.length > 1 && !payload.azp) {\n                    throw new RPError({\n                        message: \"missing required JWT property azp\",\n                        jwt\n                    });\n                }\n                if (!payload.aud.includes(this.client_id)) {\n                    throw new RPError({\n                        printf: [\n                            \"aud is missing the client_id, expected %s to be included in %j\",\n                            this.client_id,\n                            payload.aud\n                        ],\n                        jwt\n                    });\n                }\n            } else if (payload.aud !== this.client_id) {\n                throw new RPError({\n                    printf: [\n                        \"aud mismatch, expected %s, got: %s\",\n                        this.client_id,\n                        payload.aud\n                    ],\n                    jwt\n                });\n            }\n        }\n        if (payload.azp !== undefined) {\n            let additionalAuthorizedParties = this.#additionalAuthorizedParties;\n            if (typeof additionalAuthorizedParties === \"string\") {\n                additionalAuthorizedParties = [\n                    this.client_id,\n                    additionalAuthorizedParties\n                ];\n            } else if (Array.isArray(additionalAuthorizedParties)) {\n                additionalAuthorizedParties = [\n                    this.client_id,\n                    ...additionalAuthorizedParties\n                ];\n            } else {\n                additionalAuthorizedParties = [\n                    this.client_id\n                ];\n            }\n            if (!additionalAuthorizedParties.includes(payload.azp)) {\n                throw new RPError({\n                    printf: [\n                        \"azp mismatch, got: %s\",\n                        payload.azp\n                    ],\n                    jwt\n                });\n            }\n        }\n        let keys;\n        if (isSelfIssued) {\n            try {\n                assert(isPlainObject(payload.sub_jwk));\n                const key = await jose.importJWK(payload.sub_jwk, header.alg);\n                assert.equal(key.type, \"public\");\n                keys = [\n                    {\n                        keyObject () {\n                            return key;\n                        }\n                    }\n                ];\n            } catch (err) {\n                throw new RPError({\n                    message: \"failed to use sub_jwk claim as an asymmetric JSON Web Key\",\n                    jwt\n                });\n            }\n            if (await jose.calculateJwkThumbprint(payload.sub_jwk) !== payload.sub) {\n                throw new RPError({\n                    message: \"failed to match the subject with sub_jwk\",\n                    jwt\n                });\n            }\n        } else if (header.alg.startsWith(\"HS\")) {\n            keys = [\n                this.secretForAlg(header.alg)\n            ];\n        } else if (header.alg !== \"none\") {\n            keys = await queryKeyStore.call(this.issuer, {\n                ...header,\n                use: \"sig\"\n            });\n        }\n        if (!keys && header.alg === \"none\") {\n            return {\n                protected: header,\n                payload\n            };\n        }\n        for (const key of keys){\n            const verified = await jose.compactVerify(jwt, key instanceof Uint8Array ? key : await key.keyObject(header.alg)).catch(()=>{});\n            if (verified) {\n                return {\n                    payload,\n                    protected: verified.protectedHeader,\n                    key\n                };\n            }\n        }\n        throw new RPError({\n            message: \"failed to validate JWT signature\",\n            jwt\n        });\n    }\n    async refresh(refreshToken, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        let token = refreshToken;\n        if (token instanceof TokenSet) {\n            if (!token.refresh_token) {\n                throw new TypeError(\"refresh_token not present in TokenSet\");\n            }\n            token = token.refresh_token;\n        }\n        const tokenset = await this.grant({\n            ...exchangeBody,\n            grant_type: \"refresh_token\",\n            refresh_token: String(token)\n        }, {\n            clientAssertionPayload,\n            DPoP\n        });\n        if (tokenset.id_token) {\n            await this.decryptIdToken(tokenset);\n            await this.validateIdToken(tokenset, skipNonceCheck, \"token\", skipMaxAgeCheck);\n            if (refreshToken instanceof TokenSet && refreshToken.id_token) {\n                const expectedSub = refreshToken.claims().sub;\n                const actualSub = tokenset.claims().sub;\n                if (actualSub !== expectedSub) {\n                    throw new RPError({\n                        printf: [\n                            \"sub mismatch, expected %s, got: %s\",\n                            expectedSub,\n                            actualSub\n                        ],\n                        jwt: tokenset.id_token\n                    });\n                }\n            }\n        }\n        return tokenset;\n    }\n    async requestResource(resourceUrl, accessToken, { method, headers, body, DPoP, tokenType = DPoP ? \"DPoP\" : accessToken instanceof TokenSet ? accessToken.token_type : \"Bearer\" } = {}, retry) {\n        if (accessToken instanceof TokenSet) {\n            if (!accessToken.access_token) {\n                throw new TypeError(\"access_token not present in TokenSet\");\n            }\n            accessToken = accessToken.access_token;\n        }\n        if (!accessToken) {\n            throw new TypeError(\"no access token provided\");\n        } else if (typeof accessToken !== \"string\") {\n            throw new TypeError(\"invalid access token provided\");\n        }\n        const requestOpts = {\n            headers: {\n                Authorization: authorizationHeaderValue(accessToken, tokenType),\n                ...headers\n            },\n            body\n        };\n        const mTLS = !!this.tls_client_certificate_bound_access_tokens;\n        const response = await request.call(this, {\n            ...requestOpts,\n            responseType: \"buffer\",\n            method,\n            url: resourceUrl\n        }, {\n            accessToken,\n            mTLS,\n            DPoP\n        });\n        const wwwAuthenticate = response.headers[\"www-authenticate\"];\n        if (retry !== retryAttempt && wwwAuthenticate && wwwAuthenticate.toLowerCase().startsWith(\"dpop \") && parseWwwAuthenticate(wwwAuthenticate).error === \"use_dpop_nonce\") {\n            return this.requestResource(resourceUrl, accessToken, {\n                method,\n                headers,\n                body,\n                DPoP,\n                tokenType\n            });\n        }\n        return response;\n    }\n    async userinfo(accessToken, { method = \"GET\", via = \"header\", tokenType, params, DPoP } = {}) {\n        assertIssuerConfiguration(this.issuer, \"userinfo_endpoint\");\n        const options = {\n            tokenType,\n            method: String(method).toUpperCase(),\n            DPoP\n        };\n        if (options.method !== \"GET\" && options.method !== \"POST\") {\n            throw new TypeError(\"#userinfo() method can only be POST or a GET\");\n        }\n        if (via === \"body\" && options.method !== \"POST\") {\n            throw new TypeError(\"can only send body on POST\");\n        }\n        const jwt = !!(this.userinfo_signed_response_alg || this.userinfo_encrypted_response_alg);\n        if (jwt) {\n            options.headers = {\n                Accept: \"application/jwt\"\n            };\n        } else {\n            options.headers = {\n                Accept: \"application/json\"\n            };\n        }\n        const mTLS = !!this.tls_client_certificate_bound_access_tokens;\n        let targetUrl;\n        if (mTLS && this.issuer.mtls_endpoint_aliases) {\n            targetUrl = this.issuer.mtls_endpoint_aliases.userinfo_endpoint;\n        }\n        targetUrl = new URL(targetUrl || this.issuer.userinfo_endpoint);\n        if (via === \"body\") {\n            options.headers.Authorization = undefined;\n            options.headers[\"Content-Type\"] = \"application/x-www-form-urlencoded\";\n            options.body = new URLSearchParams();\n            options.body.append(\"access_token\", accessToken instanceof TokenSet ? accessToken.access_token : accessToken);\n        }\n        // handle additional parameters, GET via querystring, POST via urlencoded body\n        if (params) {\n            if (options.method === \"GET\") {\n                Object.entries(params).forEach(([key, value])=>{\n                    targetUrl.searchParams.append(key, value);\n                });\n            } else if (options.body) {\n                // POST && via body\n                Object.entries(params).forEach(([key, value])=>{\n                    options.body.append(key, value);\n                });\n            } else {\n                // POST && via header\n                options.body = new URLSearchParams();\n                options.headers[\"Content-Type\"] = \"application/x-www-form-urlencoded\";\n                Object.entries(params).forEach(([key, value])=>{\n                    options.body.append(key, value);\n                });\n            }\n        }\n        if (options.body) {\n            options.body = options.body.toString();\n        }\n        const response = await this.requestResource(targetUrl, accessToken, options);\n        let parsed = processResponse(response, {\n            bearer: true\n        });\n        if (jwt) {\n            if (!/^application\\/jwt/.test(response.headers[\"content-type\"])) {\n                throw new RPError({\n                    message: \"expected application/jwt response from the userinfo_endpoint\",\n                    response\n                });\n            }\n            const body = response.body.toString();\n            const userinfo = await this.decryptJWTUserinfo(body);\n            if (!this.userinfo_signed_response_alg) {\n                try {\n                    parsed = JSON.parse(userinfo);\n                    assert(isPlainObject(parsed));\n                } catch (err) {\n                    throw new RPError({\n                        message: \"failed to parse userinfo JWE payload as JSON\",\n                        jwt: userinfo\n                    });\n                }\n            } else {\n                ({ payload: parsed } = await this.validateJWTUserinfo(userinfo));\n            }\n        } else {\n            try {\n                parsed = JSON.parse(response.body);\n            } catch (err) {\n                Object.defineProperty(err, \"response\", {\n                    value: response\n                });\n                throw err;\n            }\n        }\n        if (accessToken instanceof TokenSet && accessToken.id_token) {\n            const expectedSub = accessToken.claims().sub;\n            if (parsed.sub !== expectedSub) {\n                throw new RPError({\n                    printf: [\n                        \"userinfo sub mismatch, expected %s, got: %s\",\n                        expectedSub,\n                        parsed.sub\n                    ],\n                    body: parsed,\n                    jwt: accessToken.id_token\n                });\n            }\n        }\n        return parsed;\n    }\n    encryptionSecret(len) {\n        const hash = len <= 256 ? \"sha256\" : len <= 384 ? \"sha384\" : len <= 512 ? \"sha512\" : false;\n        if (!hash) {\n            throw new Error(\"unsupported symmetric encryption key derivation\");\n        }\n        return crypto.createHash(hash).update(this.client_secret).digest().slice(0, len / 8);\n    }\n    secretForAlg(alg) {\n        if (!this.client_secret) {\n            throw new TypeError(\"client_secret is required\");\n        }\n        if (/^A(\\d{3})(?:GCM)?KW$/.test(alg)) {\n            return this.encryptionSecret(parseInt(RegExp.$1, 10));\n        }\n        if (/^A(\\d{3})(?:GCM|CBC-HS(\\d{3}))$/.test(alg)) {\n            return this.encryptionSecret(parseInt(RegExp.$2 || RegExp.$1, 10));\n        }\n        return new TextEncoder().encode(this.client_secret);\n    }\n    async grant(body, { clientAssertionPayload, DPoP } = {}, retry) {\n        assertIssuerConfiguration(this.issuer, \"token_endpoint\");\n        const response = await authenticatedPost.call(this, \"token\", {\n            form: body,\n            responseType: \"json\"\n        }, {\n            clientAssertionPayload,\n            DPoP\n        });\n        let responseBody;\n        try {\n            responseBody = processResponse(response);\n        } catch (err) {\n            if (retry !== retryAttempt && err instanceof OPError && err.error === \"use_dpop_nonce\") {\n                return this.grant(body, {\n                    clientAssertionPayload,\n                    DPoP\n                }, retryAttempt);\n            }\n            throw err;\n        }\n        return new TokenSet(responseBody);\n    }\n    async deviceAuthorization(params = {}, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        assertIssuerConfiguration(this.issuer, \"device_authorization_endpoint\");\n        assertIssuerConfiguration(this.issuer, \"token_endpoint\");\n        const body = authorizationParams.call(this, {\n            client_id: this.client_id,\n            redirect_uri: null,\n            response_type: null,\n            ...params\n        });\n        const response = await authenticatedPost.call(this, \"device_authorization\", {\n            responseType: \"json\",\n            form: body\n        }, {\n            clientAssertionPayload,\n            endpointAuthMethod: \"token\"\n        });\n        const responseBody = processResponse(response);\n        return new DeviceFlowHandle({\n            client: this,\n            exchangeBody,\n            clientAssertionPayload,\n            response: responseBody,\n            maxAge: params.max_age,\n            DPoP\n        });\n    }\n    async revoke(token, hint, { revokeBody, clientAssertionPayload } = {}) {\n        assertIssuerConfiguration(this.issuer, \"revocation_endpoint\");\n        if (hint !== undefined && typeof hint !== \"string\") {\n            throw new TypeError(\"hint must be a string\");\n        }\n        const form = {\n            ...revokeBody,\n            token\n        };\n        if (hint) {\n            form.token_type_hint = hint;\n        }\n        const response = await authenticatedPost.call(this, \"revocation\", {\n            form\n        }, {\n            clientAssertionPayload\n        });\n        processResponse(response, {\n            body: false\n        });\n    }\n    async introspect(token, hint, { introspectBody, clientAssertionPayload } = {}) {\n        assertIssuerConfiguration(this.issuer, \"introspection_endpoint\");\n        if (hint !== undefined && typeof hint !== \"string\") {\n            throw new TypeError(\"hint must be a string\");\n        }\n        const form = {\n            ...introspectBody,\n            token\n        };\n        if (hint) {\n            form.token_type_hint = hint;\n        }\n        const response = await authenticatedPost.call(this, \"introspection\", {\n            form,\n            responseType: \"json\"\n        }, {\n            clientAssertionPayload\n        });\n        const responseBody = processResponse(response);\n        return responseBody;\n    }\n    static async register(metadata, options = {}) {\n        const { initialAccessToken, jwks, ...clientOptions } = options;\n        assertIssuerConfiguration(this.issuer, \"registration_endpoint\");\n        if (jwks !== undefined && !(metadata.jwks || metadata.jwks_uri)) {\n            const keystore = await getKeystore.call(this, jwks);\n            metadata.jwks = keystore.toJWKS();\n        }\n        const response = await request.call(this, {\n            headers: {\n                Accept: \"application/json\",\n                ...initialAccessToken ? {\n                    Authorization: authorizationHeaderValue(initialAccessToken)\n                } : undefined\n            },\n            responseType: \"json\",\n            json: metadata,\n            url: this.issuer.registration_endpoint,\n            method: \"POST\"\n        });\n        const responseBody = processResponse(response, {\n            statusCode: 201,\n            bearer: true\n        });\n        return new this(responseBody, jwks, clientOptions);\n    }\n    get metadata() {\n        return clone(Object.fromEntries(this.#metadata.entries()));\n    }\n    static async fromUri(registrationClientUri, registrationAccessToken, jwks, clientOptions) {\n        const response = await request.call(this, {\n            method: \"GET\",\n            url: registrationClientUri,\n            responseType: \"json\",\n            headers: {\n                Authorization: authorizationHeaderValue(registrationAccessToken),\n                Accept: \"application/json\"\n            }\n        });\n        const responseBody = processResponse(response, {\n            bearer: true\n        });\n        return new this(responseBody, jwks, clientOptions);\n    }\n    async requestObject(requestObject = {}, { sign: signingAlgorithm = this.request_object_signing_alg || \"none\", encrypt: { alg: eKeyManagement = this.request_object_encryption_alg, enc: eContentEncryption = this.request_object_encryption_enc || \"A128CBC-HS256\" } = {} } = {}) {\n        if (!isPlainObject(requestObject)) {\n            throw new TypeError(\"requestObject must be a plain object\");\n        }\n        let signed;\n        let key;\n        const unix = now();\n        const header = {\n            alg: signingAlgorithm,\n            typ: \"oauth-authz-req+jwt\"\n        };\n        const payload = JSON.stringify(defaults({}, requestObject, {\n            iss: this.client_id,\n            aud: this.issuer.issuer,\n            client_id: this.client_id,\n            jti: random(),\n            iat: unix,\n            exp: unix + 300,\n            ...this.fapi() ? {\n                nbf: unix\n            } : undefined\n        }));\n        if (signingAlgorithm === \"none\") {\n            signed = [\n                base64url.encode(JSON.stringify(header)),\n                base64url.encode(payload),\n                \"\"\n            ].join(\".\");\n        } else {\n            const symmetric = signingAlgorithm.startsWith(\"HS\");\n            if (symmetric) {\n                key = this.secretForAlg(signingAlgorithm);\n            } else {\n                const keystore = await keystores.get(this);\n                if (!keystore) {\n                    throw new TypeError(`no keystore present for client, cannot sign using alg ${signingAlgorithm}`);\n                }\n                key = keystore.get({\n                    alg: signingAlgorithm,\n                    use: \"sig\"\n                });\n                if (!key) {\n                    throw new TypeError(`no key to sign with found for alg ${signingAlgorithm}`);\n                }\n            }\n            signed = await new jose.CompactSign(new TextEncoder().encode(payload)).setProtectedHeader({\n                ...header,\n                kid: symmetric ? undefined : key.jwk.kid\n            }).sign(symmetric ? key : await key.keyObject(signingAlgorithm));\n        }\n        if (!eKeyManagement) {\n            return signed;\n        }\n        const fields = {\n            alg: eKeyManagement,\n            enc: eContentEncryption,\n            cty: \"oauth-authz-req+jwt\"\n        };\n        if (fields.alg.match(/^(RSA|ECDH)/)) {\n            [key] = await queryKeyStore.call(this.issuer, {\n                alg: fields.alg,\n                use: \"enc\"\n            }, {\n                allowMulti: true\n            });\n        } else {\n            key = this.secretForAlg(fields.alg === \"dir\" ? fields.enc : fields.alg);\n        }\n        return new jose.CompactEncrypt(new TextEncoder().encode(signed)).setProtectedHeader({\n            ...fields,\n            kid: key instanceof Uint8Array ? undefined : key.jwk.kid\n        }).encrypt(key instanceof Uint8Array ? key : await key.keyObject(fields.alg));\n    }\n    async pushedAuthorizationRequest(params = {}, { clientAssertionPayload } = {}) {\n        assertIssuerConfiguration(this.issuer, \"pushed_authorization_request_endpoint\");\n        const body = {\n            ...\"request\" in params ? params : authorizationParams.call(this, params),\n            client_id: this.client_id\n        };\n        const response = await authenticatedPost.call(this, \"pushed_authorization_request\", {\n            responseType: \"json\",\n            form: body\n        }, {\n            clientAssertionPayload,\n            endpointAuthMethod: \"token\"\n        });\n        const responseBody = processResponse(response, {\n            statusCode: 201\n        });\n        if (!(\"expires_in\" in responseBody)) {\n            throw new RPError({\n                message: \"expected expires_in in Pushed Authorization Successful Response\",\n                response\n            });\n        }\n        if (typeof responseBody.expires_in !== \"number\") {\n            throw new RPError({\n                message: \"invalid expires_in value in Pushed Authorization Successful Response\",\n                response\n            });\n        }\n        if (!(\"request_uri\" in responseBody)) {\n            throw new RPError({\n                message: \"expected request_uri in Pushed Authorization Successful Response\",\n                response\n            });\n        }\n        if (typeof responseBody.request_uri !== \"string\") {\n            throw new RPError({\n                message: \"invalid request_uri value in Pushed Authorization Successful Response\",\n                response\n            });\n        }\n        return responseBody;\n    }\n    get issuer() {\n        return this.#issuer;\n    }\n    /* istanbul ignore next */ [inspect.custom]() {\n        return `${this.constructor.name} ${inspect(this.metadata, {\n            depth: Infinity,\n            colors: process.stdout.isTTY,\n            compact: false,\n            sorted: true\n        })}`;\n    }\n    fapi() {\n        return this.fapi1() || this.fapi2();\n    }\n    fapi1() {\n        return this.constructor.name === \"FAPI1Client\";\n    }\n    fapi2() {\n        return this.constructor.name === \"FAPI2Client\";\n    }\n    async validateJARM(response) {\n        const expectedAlg = this.authorization_signed_response_alg;\n        const { payload } = await this.validateJWT(response, expectedAlg, [\n            \"iss\",\n            \"exp\",\n            \"aud\"\n        ]);\n        return pickCb(payload);\n    }\n    /**\n   * @name dpopProof\n   * @api private\n   */ async dpopProof(payload, privateKeyInput, accessToken) {\n        if (!isPlainObject(payload)) {\n            throw new TypeError(\"payload must be a plain object\");\n        }\n        let privateKey;\n        if (isKeyObject(privateKeyInput)) {\n            privateKey = privateKeyInput;\n        } else if (privateKeyInput[Symbol.toStringTag] === \"CryptoKey\") {\n            privateKey = privateKeyInput;\n        } else if (jose.cryptoRuntime === \"node:crypto\") {\n            privateKey = crypto.createPrivateKey(privateKeyInput);\n        } else {\n            throw new TypeError(\"unrecognized crypto runtime\");\n        }\n        if (privateKey.type !== \"private\") {\n            throw new TypeError('\"DPoP\" option must be a private key');\n        }\n        let alg = determineDPoPAlgorithm.call(this, privateKey, privateKeyInput);\n        if (!alg) {\n            throw new TypeError(\"could not determine DPoP JWS Algorithm\");\n        }\n        return new jose.SignJWT({\n            ath: accessToken ? base64url.encode(crypto.createHash(\"sha256\").update(accessToken).digest()) : undefined,\n            ...payload\n        }).setProtectedHeader({\n            alg,\n            typ: \"dpop+jwt\",\n            jwk: await getJwk(privateKey, privateKeyInput)\n        }).setIssuedAt().setJti(random()).sign(privateKey);\n    }\n}\nfunction determineDPoPAlgorithmFromCryptoKey(cryptoKey) {\n    switch(cryptoKey.algorithm.name){\n        case \"Ed25519\":\n        case \"Ed448\":\n            return \"EdDSA\";\n        case \"ECDSA\":\n            {\n                switch(cryptoKey.algorithm.namedCurve){\n                    case \"P-256\":\n                        return \"ES256\";\n                    case \"P-384\":\n                        return \"ES384\";\n                    case \"P-521\":\n                        return \"ES512\";\n                    default:\n                        break;\n                }\n                break;\n            }\n        case \"RSASSA-PKCS1-v1_5\":\n            return `RS${cryptoKey.algorithm.hash.name.slice(4)}`;\n        case \"RSA-PSS\":\n            return `PS${cryptoKey.algorithm.hash.name.slice(4)}`;\n        default:\n            throw new TypeError(\"unsupported DPoP private key\");\n    }\n}\nlet determineDPoPAlgorithm;\nif (jose.cryptoRuntime === \"node:crypto\") {\n    determineDPoPAlgorithm = function(privateKey, privateKeyInput) {\n        if (privateKeyInput[Symbol.toStringTag] === \"CryptoKey\") {\n            return determineDPoPAlgorithmFromCryptoKey(privateKey);\n        }\n        switch(privateKey.asymmetricKeyType){\n            case \"ed25519\":\n            case \"ed448\":\n                return \"EdDSA\";\n            case \"ec\":\n                return determineEcAlgorithm(privateKey, privateKeyInput);\n            case \"rsa\":\n            case rsaPssParams && \"rsa-pss\":\n                return determineRsaAlgorithm(privateKey, privateKeyInput, this.issuer.dpop_signing_alg_values_supported);\n            default:\n                throw new TypeError(\"unsupported DPoP private key\");\n        }\n    };\n    const RSPS = /^(?:RS|PS)(?:256|384|512)$/;\n    function determineRsaAlgorithm(privateKey, privateKeyInput, valuesSupported) {\n        if (typeof privateKeyInput === \"object\" && privateKeyInput.format === \"jwk\" && privateKeyInput.key && privateKeyInput.key.alg) {\n            return privateKeyInput.key.alg;\n        }\n        if (Array.isArray(valuesSupported)) {\n            let candidates = valuesSupported.filter(RegExp.prototype.test.bind(RSPS));\n            if (privateKey.asymmetricKeyType === \"rsa-pss\") {\n                candidates = candidates.filter((value)=>value.startsWith(\"PS\"));\n            }\n            return [\n                \"PS256\",\n                \"PS384\",\n                \"PS512\",\n                \"RS256\",\n                \"RS384\",\n                \"RS384\"\n            ].find((preferred)=>candidates.includes(preferred));\n        }\n        return \"PS256\";\n    }\n    const p256 = Buffer.from([\n        42,\n        134,\n        72,\n        206,\n        61,\n        3,\n        1,\n        7\n    ]);\n    const p384 = Buffer.from([\n        43,\n        129,\n        4,\n        0,\n        34\n    ]);\n    const p521 = Buffer.from([\n        43,\n        129,\n        4,\n        0,\n        35\n    ]);\n    const secp256k1 = Buffer.from([\n        43,\n        129,\n        4,\n        0,\n        10\n    ]);\n    function determineEcAlgorithm(privateKey, privateKeyInput) {\n        // If input was a JWK\n        switch(typeof privateKeyInput === \"object\" && typeof privateKeyInput.key === \"object\" && privateKeyInput.key.crv){\n            case \"P-256\":\n                return \"ES256\";\n            case \"secp256k1\":\n                return \"ES256K\";\n            case \"P-384\":\n                return \"ES384\";\n            case \"P-512\":\n                return \"ES512\";\n            default:\n                break;\n        }\n        const buf = privateKey.export({\n            format: \"der\",\n            type: \"pkcs8\"\n        });\n        const i = buf[1] < 128 ? 17 : 18;\n        const len = buf[i];\n        const curveOid = buf.slice(i + 1, i + 1 + len);\n        if (curveOid.equals(p256)) {\n            return \"ES256\";\n        }\n        if (curveOid.equals(p384)) {\n            return \"ES384\";\n        }\n        if (curveOid.equals(p521)) {\n            return \"ES512\";\n        }\n        if (curveOid.equals(secp256k1)) {\n            return \"ES256K\";\n        }\n        throw new TypeError(\"unsupported DPoP private key curve\");\n    }\n} else {\n    determineDPoPAlgorithm = determineDPoPAlgorithmFromCryptoKey;\n}\nconst jwkCache = new WeakMap();\nasync function getJwk(keyObject, privateKeyInput) {\n    if (jose.cryptoRuntime === \"node:crypto\" && typeof privateKeyInput === \"object\" && typeof privateKeyInput.key === \"object\" && privateKeyInput.format === \"jwk\") {\n        return pick(privateKeyInput.key, \"kty\", \"crv\", \"x\", \"y\", \"e\", \"n\");\n    }\n    if (jwkCache.has(privateKeyInput)) {\n        return jwkCache.get(privateKeyInput);\n    }\n    const jwk = pick(await jose.exportJWK(keyObject), \"kty\", \"crv\", \"x\", \"y\", \"e\", \"n\");\n    if (isKeyObject(privateKeyInput) || jose.cryptoRuntime === \"WebCryptoAPI\") {\n        jwkCache.set(privateKeyInput, jwk);\n    }\n    return jwk;\n}\nmodule.exports = (issuer, aadIssValidation = false)=>class Client extends BaseClient {\n        constructor(...args){\n            super(issuer, aadIssValidation, ...args);\n        }\n        static get issuer() {\n            return issuer;\n        }\n    };\nmodule.exports.BaseClient = BaseClient;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/device_flow_handle.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/device_flow_handle.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { inspect } = __webpack_require__(/*! util */ \"util\");\nconst { RPError, OPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst now = __webpack_require__(/*! ./helpers/unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\nclass DeviceFlowHandle {\n    #aborted;\n    #client;\n    #clientAssertionPayload;\n    #DPoP;\n    #exchangeBody;\n    #expires_at;\n    #interval;\n    #maxAge;\n    #response;\n    constructor({ client, exchangeBody, clientAssertionPayload, response, maxAge, DPoP }){\n        [\n            \"verification_uri\",\n            \"user_code\",\n            \"device_code\"\n        ].forEach((prop)=>{\n            if (typeof response[prop] !== \"string\" || !response[prop]) {\n                throw new RPError(`expected ${prop} string to be returned by Device Authorization Response, got %j`, response[prop]);\n            }\n        });\n        if (!Number.isSafeInteger(response.expires_in)) {\n            throw new RPError(\"expected expires_in number to be returned by Device Authorization Response, got %j\", response.expires_in);\n        }\n        this.#expires_at = now() + response.expires_in;\n        this.#client = client;\n        this.#DPoP = DPoP;\n        this.#maxAge = maxAge;\n        this.#exchangeBody = exchangeBody;\n        this.#clientAssertionPayload = clientAssertionPayload;\n        this.#response = response;\n        this.#interval = response.interval * 1000 || 5000;\n    }\n    abort() {\n        this.#aborted = true;\n    }\n    async poll({ signal } = {}) {\n        if (signal && signal.aborted || this.#aborted) {\n            throw new RPError(\"polling aborted\");\n        }\n        if (this.expired()) {\n            throw new RPError(\"the device code %j has expired and the device authorization session has concluded\", this.device_code);\n        }\n        await new Promise((resolve)=>setTimeout(resolve, this.#interval));\n        let tokenset;\n        try {\n            tokenset = await this.#client.grant({\n                ...this.#exchangeBody,\n                grant_type: \"urn:ietf:params:oauth:grant-type:device_code\",\n                device_code: this.device_code\n            }, {\n                clientAssertionPayload: this.#clientAssertionPayload,\n                DPoP: this.#DPoP\n            });\n        } catch (err) {\n            switch(err instanceof OPError && err.error){\n                case \"slow_down\":\n                    this.#interval += 5000;\n                case \"authorization_pending\":\n                    return this.poll({\n                        signal\n                    });\n                default:\n                    throw err;\n            }\n        }\n        if (\"id_token\" in tokenset) {\n            await this.#client.decryptIdToken(tokenset);\n            await this.#client.validateIdToken(tokenset, undefined, \"token\", this.#maxAge);\n        }\n        return tokenset;\n    }\n    get device_code() {\n        return this.#response.device_code;\n    }\n    get user_code() {\n        return this.#response.user_code;\n    }\n    get verification_uri() {\n        return this.#response.verification_uri;\n    }\n    get verification_uri_complete() {\n        return this.#response.verification_uri_complete;\n    }\n    get expires_in() {\n        return Math.max.apply(null, [\n            this.#expires_at - now(),\n            0\n        ]);\n    }\n    expired() {\n        return this.expires_in === 0;\n    }\n    /* istanbul ignore next */ [inspect.custom]() {\n        return `${this.constructor.name} ${inspect(this.#response, {\n            depth: Infinity,\n            colors: process.stdout.isTTY,\n            compact: false,\n            sorted: true\n        })}`;\n    }\n}\nmodule.exports = DeviceFlowHandle;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/device_flow_handle.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/errors.js":
/*!**************************************************!*\
  !*** ./node_modules/openid-client/lib/errors.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { format } = __webpack_require__(/*! util */ \"util\");\nclass OPError extends Error {\n    constructor({ error_description, error, error_uri, session_state, state, scope }, response){\n        super(!error_description ? error : `${error} (${error_description})`);\n        Object.assign(this, {\n            error\n        }, error_description && {\n            error_description\n        }, error_uri && {\n            error_uri\n        }, state && {\n            state\n        }, scope && {\n            scope\n        }, session_state && {\n            session_state\n        });\n        if (response) {\n            Object.defineProperty(this, \"response\", {\n                value: response\n            });\n        }\n        this.name = this.constructor.name;\n        Error.captureStackTrace(this, this.constructor);\n    }\n}\nclass RPError extends Error {\n    constructor(...args){\n        if (typeof args[0] === \"string\") {\n            super(format(...args));\n        } else {\n            const { message, printf, response, ...rest } = args[0];\n            if (printf) {\n                super(format(...printf));\n            } else {\n                super(message);\n            }\n            Object.assign(this, rest);\n            if (response) {\n                Object.defineProperty(this, \"response\", {\n                    value: response\n                });\n            }\n        }\n        this.name = this.constructor.name;\n        Error.captureStackTrace(this, this.constructor);\n    }\n}\nmodule.exports = {\n    OPError,\n    RPError\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvZXJyb3JzLmpzIiwibWFwcGluZ3MiOiJBQUFBLE1BQU0sRUFBRUEsTUFBTSxFQUFFLEdBQUdDLG1CQUFPQSxDQUFDO0FBRTNCLE1BQU1DLGdCQUFnQkM7SUFDcEJDLFlBQVksRUFBRUMsaUJBQWlCLEVBQUVDLEtBQUssRUFBRUMsU0FBUyxFQUFFQyxhQUFhLEVBQUVDLEtBQUssRUFBRUMsS0FBSyxFQUFFLEVBQUVDLFFBQVEsQ0FBRTtRQUMxRixLQUFLLENBQUMsQ0FBQ04sb0JBQW9CQyxRQUFRLENBQUMsRUFBRUEsTUFBTSxFQUFFLEVBQUVELGtCQUFrQixDQUFDLENBQUM7UUFFcEVPLE9BQU9DLE1BQU0sQ0FDWCxJQUFJLEVBQ0o7WUFBRVA7UUFBTSxHQUNSRCxxQkFBcUI7WUFBRUE7UUFBa0IsR0FDekNFLGFBQWE7WUFBRUE7UUFBVSxHQUN6QkUsU0FBUztZQUFFQTtRQUFNLEdBQ2pCQyxTQUFTO1lBQUVBO1FBQU0sR0FDakJGLGlCQUFpQjtZQUFFQTtRQUFjO1FBR25DLElBQUlHLFVBQVU7WUFDWkMsT0FBT0UsY0FBYyxDQUFDLElBQUksRUFBRSxZQUFZO2dCQUN0Q0MsT0FBT0o7WUFDVDtRQUNGO1FBRUEsSUFBSSxDQUFDSyxJQUFJLEdBQUcsSUFBSSxDQUFDWixXQUFXLENBQUNZLElBQUk7UUFDakNiLE1BQU1jLGlCQUFpQixDQUFDLElBQUksRUFBRSxJQUFJLENBQUNiLFdBQVc7SUFDaEQ7QUFDRjtBQUVBLE1BQU1jLGdCQUFnQmY7SUFDcEJDLFlBQVksR0FBR2UsSUFBSSxDQUFFO1FBQ25CLElBQUksT0FBT0EsSUFBSSxDQUFDLEVBQUUsS0FBSyxVQUFVO1lBQy9CLEtBQUssQ0FBQ25CLFVBQVVtQjtRQUNsQixPQUFPO1lBQ0wsTUFBTSxFQUFFQyxPQUFPLEVBQUVDLE1BQU0sRUFBRVYsUUFBUSxFQUFFLEdBQUdXLE1BQU0sR0FBR0gsSUFBSSxDQUFDLEVBQUU7WUFDdEQsSUFBSUUsUUFBUTtnQkFDVixLQUFLLENBQUNyQixVQUFVcUI7WUFDbEIsT0FBTztnQkFDTCxLQUFLLENBQUNEO1lBQ1I7WUFDQVIsT0FBT0MsTUFBTSxDQUFDLElBQUksRUFBRVM7WUFDcEIsSUFBSVgsVUFBVTtnQkFDWkMsT0FBT0UsY0FBYyxDQUFDLElBQUksRUFBRSxZQUFZO29CQUN0Q0MsT0FBT0o7Z0JBQ1Q7WUFDRjtRQUNGO1FBRUEsSUFBSSxDQUFDSyxJQUFJLEdBQUcsSUFBSSxDQUFDWixXQUFXLENBQUNZLElBQUk7UUFDakNiLE1BQU1jLGlCQUFpQixDQUFDLElBQUksRUFBRSxJQUFJLENBQUNiLFdBQVc7SUFDaEQ7QUFDRjtBQUVBbUIsT0FBT0MsT0FBTyxHQUFHO0lBQ2Z0QjtJQUNBZ0I7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL2Nocm9ub3MtcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvZXJyb3JzLmpzP2E3NDEiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgeyBmb3JtYXQgfSA9IHJlcXVpcmUoJ3V0aWwnKTtcblxuY2xhc3MgT1BFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgY29uc3RydWN0b3IoeyBlcnJvcl9kZXNjcmlwdGlvbiwgZXJyb3IsIGVycm9yX3VyaSwgc2Vzc2lvbl9zdGF0ZSwgc3RhdGUsIHNjb3BlIH0sIHJlc3BvbnNlKSB7XG4gICAgc3VwZXIoIWVycm9yX2Rlc2NyaXB0aW9uID8gZXJyb3IgOiBgJHtlcnJvcn0gKCR7ZXJyb3JfZGVzY3JpcHRpb259KWApO1xuXG4gICAgT2JqZWN0LmFzc2lnbihcbiAgICAgIHRoaXMsXG4gICAgICB7IGVycm9yIH0sXG4gICAgICBlcnJvcl9kZXNjcmlwdGlvbiAmJiB7IGVycm9yX2Rlc2NyaXB0aW9uIH0sXG4gICAgICBlcnJvcl91cmkgJiYgeyBlcnJvcl91cmkgfSxcbiAgICAgIHN0YXRlICYmIHsgc3RhdGUgfSxcbiAgICAgIHNjb3BlICYmIHsgc2NvcGUgfSxcbiAgICAgIHNlc3Npb25fc3RhdGUgJiYgeyBzZXNzaW9uX3N0YXRlIH0sXG4gICAgKTtcblxuICAgIGlmIChyZXNwb25zZSkge1xuICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsICdyZXNwb25zZScsIHtcbiAgICAgICAgdmFsdWU6IHJlc3BvbnNlLFxuICAgICAgfSk7XG4gICAgfVxuXG4gICAgdGhpcy5uYW1lID0gdGhpcy5jb25zdHJ1Y3Rvci5uYW1lO1xuICAgIEVycm9yLmNhcHR1cmVTdGFja1RyYWNlKHRoaXMsIHRoaXMuY29uc3RydWN0b3IpO1xuICB9XG59XG5cbmNsYXNzIFJQRXJyb3IgZXh0ZW5kcyBFcnJvciB7XG4gIGNvbnN0cnVjdG9yKC4uLmFyZ3MpIHtcbiAgICBpZiAodHlwZW9mIGFyZ3NbMF0gPT09ICdzdHJpbmcnKSB7XG4gICAgICBzdXBlcihmb3JtYXQoLi4uYXJncykpO1xuICAgIH0gZWxzZSB7XG4gICAgICBjb25zdCB7IG1lc3NhZ2UsIHByaW50ZiwgcmVzcG9uc2UsIC4uLnJlc3QgfSA9IGFyZ3NbMF07XG4gICAgICBpZiAocHJpbnRmKSB7XG4gICAgICAgIHN1cGVyKGZvcm1hdCguLi5wcmludGYpKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHN1cGVyKG1lc3NhZ2UpO1xuICAgICAgfVxuICAgICAgT2JqZWN0LmFzc2lnbih0aGlzLCByZXN0KTtcbiAgICAgIGlmIChyZXNwb25zZSkge1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgJ3Jlc3BvbnNlJywge1xuICAgICAgICAgIHZhbHVlOiByZXNwb25zZSxcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgdGhpcy5uYW1lID0gdGhpcy5jb25zdHJ1Y3Rvci5uYW1lO1xuICAgIEVycm9yLmNhcHR1cmVTdGFja1RyYWNlKHRoaXMsIHRoaXMuY29uc3RydWN0b3IpO1xuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBPUEVycm9yLFxuICBSUEVycm9yLFxufTtcbiJdLCJuYW1lcyI6WyJmb3JtYXQiLCJyZXF1aXJlIiwiT1BFcnJvciIsIkVycm9yIiwiY29uc3RydWN0b3IiLCJlcnJvcl9kZXNjcmlwdGlvbiIsImVycm9yIiwiZXJyb3JfdXJpIiwic2Vzc2lvbl9zdGF0ZSIsInN0YXRlIiwic2NvcGUiLCJyZXNwb25zZSIsIk9iamVjdCIsImFzc2lnbiIsImRlZmluZVByb3BlcnR5IiwidmFsdWUiLCJuYW1lIiwiY2FwdHVyZVN0YWNrVHJhY2UiLCJSUEVycm9yIiwiYXJncyIsIm1lc3NhZ2UiLCJwcmludGYiLCJyZXN0IiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/errors.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/assert.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/assert.js ***!
  \**********************************************************/
/***/ ((module) => {

eval("function assertSigningAlgValuesSupport(endpoint, issuer, properties) {\n    if (!issuer[`${endpoint}_endpoint`]) return;\n    const eam = `${endpoint}_endpoint_auth_method`;\n    const easa = `${endpoint}_endpoint_auth_signing_alg`;\n    const easavs = `${endpoint}_endpoint_auth_signing_alg_values_supported`;\n    if (properties[eam] && properties[eam].endsWith(\"_jwt\") && !properties[easa] && !issuer[easavs]) {\n        throw new TypeError(`${easavs} must be configured on the issuer if ${easa} is not defined on a client`);\n    }\n}\nfunction assertIssuerConfiguration(issuer, endpoint) {\n    if (!issuer[endpoint]) {\n        throw new TypeError(`${endpoint} must be configured on the issuer`);\n    }\n}\nmodule.exports = {\n    assertSigningAlgValuesSupport,\n    assertIssuerConfiguration\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/assert.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/base64url.js":
/*!*************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/base64url.js ***!
  \*************************************************************/
/***/ ((module) => {

eval("let encode;\nif (Buffer.isEncoding(\"base64url\")) {\n    encode = (input, encoding = \"utf8\")=>Buffer.from(input, encoding).toString(\"base64url\");\n} else {\n    const fromBase64 = (base64)=>base64.replace(/=/g, \"\").replace(/\\+/g, \"-\").replace(/\\//g, \"_\");\n    encode = (input, encoding = \"utf8\")=>fromBase64(Buffer.from(input, encoding).toString(\"base64\"));\n}\nconst decode = (input)=>Buffer.from(input, \"base64\");\nmodule.exports.decode = decode;\nmodule.exports.encode = encode;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9iYXNlNjR1cmwuanMiLCJtYXBwaW5ncyI6IkFBQUEsSUFBSUE7QUFDSixJQUFJQyxPQUFPQyxVQUFVLENBQUMsY0FBYztJQUNsQ0YsU0FBUyxDQUFDRyxPQUFPQyxXQUFXLE1BQU0sR0FBS0gsT0FBT0ksSUFBSSxDQUFDRixPQUFPQyxVQUFVRSxRQUFRLENBQUM7QUFDL0UsT0FBTztJQUNMLE1BQU1DLGFBQWEsQ0FBQ0MsU0FBV0EsT0FBT0MsT0FBTyxDQUFDLE1BQU0sSUFBSUEsT0FBTyxDQUFDLE9BQU8sS0FBS0EsT0FBTyxDQUFDLE9BQU87SUFDM0ZULFNBQVMsQ0FBQ0csT0FBT0MsV0FBVyxNQUFNLEdBQ2hDRyxXQUFXTixPQUFPSSxJQUFJLENBQUNGLE9BQU9DLFVBQVVFLFFBQVEsQ0FBQztBQUNyRDtBQUVBLE1BQU1JLFNBQVMsQ0FBQ1AsUUFBVUYsT0FBT0ksSUFBSSxDQUFDRixPQUFPO0FBRTdDUSxxQkFBcUIsR0FBR0Q7QUFDeEJDLHFCQUFxQixHQUFHWCIsInNvdXJjZXMiOlsid2VicGFjazovL2Nocm9ub3MtcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9iYXNlNjR1cmwuanM/M2I1ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJsZXQgZW5jb2RlO1xuaWYgKEJ1ZmZlci5pc0VuY29kaW5nKCdiYXNlNjR1cmwnKSkge1xuICBlbmNvZGUgPSAoaW5wdXQsIGVuY29kaW5nID0gJ3V0ZjgnKSA9PiBCdWZmZXIuZnJvbShpbnB1dCwgZW5jb2RpbmcpLnRvU3RyaW5nKCdiYXNlNjR1cmwnKTtcbn0gZWxzZSB7XG4gIGNvbnN0IGZyb21CYXNlNjQgPSAoYmFzZTY0KSA9PiBiYXNlNjQucmVwbGFjZSgvPS9nLCAnJykucmVwbGFjZSgvXFwrL2csICctJykucmVwbGFjZSgvXFwvL2csICdfJyk7XG4gIGVuY29kZSA9IChpbnB1dCwgZW5jb2RpbmcgPSAndXRmOCcpID0+XG4gICAgZnJvbUJhc2U2NChCdWZmZXIuZnJvbShpbnB1dCwgZW5jb2RpbmcpLnRvU3RyaW5nKCdiYXNlNjQnKSk7XG59XG5cbmNvbnN0IGRlY29kZSA9IChpbnB1dCkgPT4gQnVmZmVyLmZyb20oaW5wdXQsICdiYXNlNjQnKTtcblxubW9kdWxlLmV4cG9ydHMuZGVjb2RlID0gZGVjb2RlO1xubW9kdWxlLmV4cG9ydHMuZW5jb2RlID0gZW5jb2RlO1xuIl0sIm5hbWVzIjpbImVuY29kZSIsIkJ1ZmZlciIsImlzRW5jb2RpbmciLCJpbnB1dCIsImVuY29kaW5nIiwiZnJvbSIsInRvU3RyaW5nIiwiZnJvbUJhc2U2NCIsImJhc2U2NCIsInJlcGxhY2UiLCJkZWNvZGUiLCJtb2R1bGUiLCJleHBvcnRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/client.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/client.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const jose = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/cjs/index.js\");\nconst { RPError } = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst { assertIssuerConfiguration } = __webpack_require__(/*! ./assert */ \"(rsc)/./node_modules/openid-client/lib/helpers/assert.js\");\nconst { random } = __webpack_require__(/*! ./generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst now = __webpack_require__(/*! ./unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\nconst request = __webpack_require__(/*! ./request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nconst { keystores } = __webpack_require__(/*! ./weak_cache */ \"(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\");\nconst merge = __webpack_require__(/*! ./merge */ \"(rsc)/./node_modules/openid-client/lib/helpers/merge.js\");\n// TODO: in v6.x additionally encode the `- _ . ! ~ * ' ( )` characters\n// https://github.com/panva/node-openid-client/commit/5a2ea80ef5e59ec0c03dbd97d82f551e24a9d348\nconst formUrlEncode = (value)=>encodeURIComponent(value).replace(/%20/g, \"+\");\nasync function clientAssertion(endpoint, payload) {\n    let alg = this[`${endpoint}_endpoint_auth_signing_alg`];\n    if (!alg) {\n        assertIssuerConfiguration(this.issuer, `${endpoint}_endpoint_auth_signing_alg_values_supported`);\n    }\n    if (this[`${endpoint}_endpoint_auth_method`] === \"client_secret_jwt\") {\n        if (!alg) {\n            const supported = this.issuer[`${endpoint}_endpoint_auth_signing_alg_values_supported`];\n            alg = Array.isArray(supported) && supported.find((signAlg)=>/^HS(?:256|384|512)/.test(signAlg));\n        }\n        if (!alg) {\n            throw new RPError(`failed to determine a JWS Algorithm to use for ${this[`${endpoint}_endpoint_auth_method`]} Client Assertion`);\n        }\n        return new jose.CompactSign(Buffer.from(JSON.stringify(payload))).setProtectedHeader({\n            alg\n        }).sign(this.secretForAlg(alg));\n    }\n    const keystore = await keystores.get(this);\n    if (!keystore) {\n        throw new TypeError(\"no client jwks provided for signing a client assertion with\");\n    }\n    if (!alg) {\n        const supported = this.issuer[`${endpoint}_endpoint_auth_signing_alg_values_supported`];\n        alg = Array.isArray(supported) && supported.find((signAlg)=>keystore.get({\n                alg: signAlg,\n                use: \"sig\"\n            }));\n    }\n    if (!alg) {\n        throw new RPError(`failed to determine a JWS Algorithm to use for ${this[`${endpoint}_endpoint_auth_method`]} Client Assertion`);\n    }\n    const key = keystore.get({\n        alg,\n        use: \"sig\"\n    });\n    if (!key) {\n        throw new RPError(`no key found in client jwks to sign a client assertion with using alg ${alg}`);\n    }\n    return new jose.CompactSign(Buffer.from(JSON.stringify(payload))).setProtectedHeader({\n        alg,\n        kid: key.jwk && key.jwk.kid\n    }).sign(await key.keyObject(alg));\n}\nasync function authFor(endpoint, { clientAssertionPayload } = {}) {\n    const authMethod = this[`${endpoint}_endpoint_auth_method`];\n    switch(authMethod){\n        case \"self_signed_tls_client_auth\":\n        case \"tls_client_auth\":\n        case \"none\":\n            return {\n                form: {\n                    client_id: this.client_id\n                }\n            };\n        case \"client_secret_post\":\n            if (typeof this.client_secret !== \"string\") {\n                throw new TypeError(\"client_secret_post client authentication method requires a client_secret\");\n            }\n            return {\n                form: {\n                    client_id: this.client_id,\n                    client_secret: this.client_secret\n                }\n            };\n        case \"private_key_jwt\":\n        case \"client_secret_jwt\":\n            {\n                const timestamp = now();\n                const assertion = await clientAssertion.call(this, endpoint, {\n                    iat: timestamp,\n                    exp: timestamp + 60,\n                    jti: random(),\n                    iss: this.client_id,\n                    sub: this.client_id,\n                    aud: this.issuer.issuer,\n                    ...clientAssertionPayload\n                });\n                return {\n                    form: {\n                        client_id: this.client_id,\n                        client_assertion: assertion,\n                        client_assertion_type: \"urn:ietf:params:oauth:client-assertion-type:jwt-bearer\"\n                    }\n                };\n            }\n        case \"client_secret_basic\":\n            {\n                // This is correct behaviour, see https://tools.ietf.org/html/rfc6749#section-2.3.1 and the\n                // related appendix. (also https://github.com/panva/node-openid-client/pull/91)\n                // > The client identifier is encoded using the\n                // > \"application/x-www-form-urlencoded\" encoding algorithm per\n                // > Appendix B, and the encoded value is used as the username; the client\n                // > password is encoded using the same algorithm and used as the\n                // > password.\n                if (typeof this.client_secret !== \"string\") {\n                    throw new TypeError(\"client_secret_basic client authentication method requires a client_secret\");\n                }\n                const encoded = `${formUrlEncode(this.client_id)}:${formUrlEncode(this.client_secret)}`;\n                const value = Buffer.from(encoded).toString(\"base64\");\n                return {\n                    headers: {\n                        Authorization: `Basic ${value}`\n                    }\n                };\n            }\n        default:\n            {\n                throw new TypeError(`missing, or unsupported, ${endpoint}_endpoint_auth_method`);\n            }\n    }\n}\nfunction resolveResponseType() {\n    const { length, 0: value } = this.response_types;\n    if (length === 1) {\n        return value;\n    }\n    return undefined;\n}\nfunction resolveRedirectUri() {\n    const { length, 0: value } = this.redirect_uris || [];\n    if (length === 1) {\n        return value;\n    }\n    return undefined;\n}\nasync function authenticatedPost(endpoint, opts, { clientAssertionPayload, endpointAuthMethod = endpoint, DPoP } = {}) {\n    const auth = await authFor.call(this, endpointAuthMethod, {\n        clientAssertionPayload\n    });\n    const requestOpts = merge(opts, auth);\n    const mTLS = this[`${endpointAuthMethod}_endpoint_auth_method`].includes(\"tls_client_auth\") || endpoint === \"token\" && this.tls_client_certificate_bound_access_tokens;\n    let targetUrl;\n    if (mTLS && this.issuer.mtls_endpoint_aliases) {\n        targetUrl = this.issuer.mtls_endpoint_aliases[`${endpoint}_endpoint`];\n    }\n    targetUrl = targetUrl || this.issuer[`${endpoint}_endpoint`];\n    if (\"form\" in requestOpts) {\n        for (const [key, value] of Object.entries(requestOpts.form)){\n            if (typeof value === \"undefined\") {\n                delete requestOpts.form[key];\n            }\n        }\n    }\n    return request.call(this, {\n        ...requestOpts,\n        method: \"POST\",\n        url: targetUrl,\n        headers: {\n            ...endpoint !== \"revocation\" ? {\n                Accept: \"application/json\"\n            } : undefined,\n            ...requestOpts.headers\n        }\n    }, {\n        mTLS,\n        DPoP\n    });\n}\nmodule.exports = {\n    resolveResponseType,\n    resolveRedirectUri,\n    authFor,\n    authenticatedPost\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/consts.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/consts.js ***!
  \**********************************************************/
/***/ ((module) => {

eval("const HTTP_OPTIONS = Symbol();\nconst CLOCK_TOLERANCE = Symbol();\nmodule.exports = {\n    CLOCK_TOLERANCE,\n    HTTP_OPTIONS\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaHJvbm9zLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL2hlbHBlcnMvY29uc3RzLmpzP2UwNTMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgSFRUUF9PUFRJT05TID0gU3ltYm9sKCk7XG5jb25zdCBDTE9DS19UT0xFUkFOQ0UgPSBTeW1ib2woKTtcblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIENMT0NLX1RPTEVSQU5DRSxcbiAgSFRUUF9PUFRJT05TLFxufTtcbiJdLCJuYW1lcyI6WyJIVFRQX09QVElPTlMiLCJTeW1ib2wiLCJDTE9DS19UT0xFUkFOQ0UiLCJtb2R1bGUiLCJleHBvcnRzIl0sIm1hcHBpbmdzIjoiQUFBQSxNQUFNQSxlQUFlQztBQUNyQixNQUFNQyxrQkFBa0JEO0FBRXhCRSxPQUFPQyxPQUFPLEdBQUc7SUFDZkY7SUFDQUY7QUFDRiIsImZpbGUiOiIocnNjKS8uL25vZGVfbW9kdWxlcy9vcGVuaWQtY2xpZW50L2xpYi9oZWxwZXJzL2NvbnN0cy5qcyIsInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/consts.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/decode_jwt.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/decode_jwt.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const base64url = __webpack_require__(/*! ./base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\nmodule.exports = (token)=>{\n    if (typeof token !== \"string\" || !token) {\n        throw new TypeError(\"JWT must be a string\");\n    }\n    const { 0: header, 1: payload, 2: signature, length } = token.split(\".\");\n    if (length === 5) {\n        throw new TypeError(\"encrypted JWTs cannot be decoded\");\n    }\n    if (length !== 3) {\n        throw new Error(\"JWTs must have three components\");\n    }\n    try {\n        return {\n            header: JSON.parse(base64url.decode(header)),\n            payload: JSON.parse(base64url.decode(payload)),\n            signature\n        };\n    } catch (err) {\n        throw new Error(\"JWT is malformed\");\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/decode_jwt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/deep_clone.js ***!
  \**************************************************************/
/***/ ((module) => {

eval("module.exports = globalThis.structuredClone || ((obj)=>JSON.parse(JSON.stringify(obj)));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaHJvbm9zLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL2hlbHBlcnMvZGVlcF9jbG9uZS5qcz84ZTRiIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gZ2xvYmFsVGhpcy5zdHJ1Y3R1cmVkQ2xvbmUgfHwgKChvYmopID0+IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkob2JqKSkpO1xuIl0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiLCJnbG9iYWxUaGlzIiwic3RydWN0dXJlZENsb25lIiwib2JqIiwiSlNPTiIsInBhcnNlIiwic3RyaW5naWZ5Il0sIm1hcHBpbmdzIjoiQUFBQUEsT0FBT0MsT0FBTyxHQUFHQyxXQUFXQyxlQUFlLElBQUssQ0FBQSxDQUFDQyxNQUFRQyxLQUFLQyxLQUFLLENBQUNELEtBQUtFLFNBQVMsQ0FBQ0gsS0FBSSIsImZpbGUiOiIocnNjKS8uL25vZGVfbW9kdWxlcy9vcGVuaWQtY2xpZW50L2xpYi9oZWxwZXJzL2RlZXBfY2xvbmUuanMiLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/defaults.js":
/*!************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/defaults.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const isPlainObject = __webpack_require__(/*! ./is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\nfunction defaults(deep, target, ...sources) {\n    for (const source of sources){\n        if (!isPlainObject(source)) {\n            continue;\n        }\n        for (const [key, value] of Object.entries(source)){\n            /* istanbul ignore if */ if (key === \"__proto__\" || key === \"constructor\") {\n                continue;\n            }\n            if (typeof target[key] === \"undefined\" && typeof value !== \"undefined\") {\n                target[key] = value;\n            }\n            if (deep && isPlainObject(target[key]) && isPlainObject(value)) {\n                defaults(true, target[key], value);\n            }\n        }\n    }\n    return target;\n}\nmodule.exports = defaults.bind(undefined, false);\nmodule.exports.deep = defaults.bind(undefined, true);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/defaults.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/generators.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/generators.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { createHash, randomBytes } = __webpack_require__(/*! crypto */ \"crypto\");\nconst base64url = __webpack_require__(/*! ./base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\nconst random = (bytes = 32)=>base64url.encode(randomBytes(bytes));\nmodule.exports = {\n    random,\n    state: random,\n    nonce: random,\n    codeVerifier: random,\n    codeChallenge: (codeVerifier)=>base64url.encode(createHash(\"sha256\").update(codeVerifier).digest())\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9nZW5lcmF0b3JzLmpzIiwibWFwcGluZ3MiOiJBQUFBLE1BQU0sRUFBRUEsVUFBVSxFQUFFQyxXQUFXLEVBQUUsR0FBR0MsbUJBQU9BLENBQUM7QUFFNUMsTUFBTUMsWUFBWUQsbUJBQU9BLENBQUM7QUFFMUIsTUFBTUUsU0FBUyxDQUFDQyxRQUFRLEVBQUUsR0FBS0YsVUFBVUcsTUFBTSxDQUFDTCxZQUFZSTtBQUU1REUsT0FBT0MsT0FBTyxHQUFHO0lBQ2ZKO0lBQ0FLLE9BQU9MO0lBQ1BNLE9BQU9OO0lBQ1BPLGNBQWNQO0lBQ2RRLGVBQWUsQ0FBQ0QsZUFDZFIsVUFBVUcsTUFBTSxDQUFDTixXQUFXLFVBQVVhLE1BQU0sQ0FBQ0YsY0FBY0csTUFBTTtBQUNyRSIsInNvdXJjZXMiOlsid2VicGFjazovL2Nocm9ub3MtcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9nZW5lcmF0b3JzLmpzPzgyZjgiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgeyBjcmVhdGVIYXNoLCByYW5kb21CeXRlcyB9ID0gcmVxdWlyZSgnY3J5cHRvJyk7XG5cbmNvbnN0IGJhc2U2NHVybCA9IHJlcXVpcmUoJy4vYmFzZTY0dXJsJyk7XG5cbmNvbnN0IHJhbmRvbSA9IChieXRlcyA9IDMyKSA9PiBiYXNlNjR1cmwuZW5jb2RlKHJhbmRvbUJ5dGVzKGJ5dGVzKSk7XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICByYW5kb20sXG4gIHN0YXRlOiByYW5kb20sXG4gIG5vbmNlOiByYW5kb20sXG4gIGNvZGVWZXJpZmllcjogcmFuZG9tLFxuICBjb2RlQ2hhbGxlbmdlOiAoY29kZVZlcmlmaWVyKSA9PlxuICAgIGJhc2U2NHVybC5lbmNvZGUoY3JlYXRlSGFzaCgnc2hhMjU2JykudXBkYXRlKGNvZGVWZXJpZmllcikuZGlnZXN0KCkpLFxufTtcbiJdLCJuYW1lcyI6WyJjcmVhdGVIYXNoIiwicmFuZG9tQnl0ZXMiLCJyZXF1aXJlIiwiYmFzZTY0dXJsIiwicmFuZG9tIiwiYnl0ZXMiLCJlbmNvZGUiLCJtb2R1bGUiLCJleHBvcnRzIiwic3RhdGUiLCJub25jZSIsImNvZGVWZXJpZmllciIsImNvZGVDaGFsbGVuZ2UiLCJ1cGRhdGUiLCJkaWdlc3QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/generators.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/is_key_object.js":
/*!*****************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/is_key_object.js ***!
  \*****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const util = __webpack_require__(/*! util */ \"util\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nmodule.exports = util.types.isKeyObject || ((obj)=>obj && obj instanceof crypto.KeyObject);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9pc19rZXlfb2JqZWN0LmpzIiwibWFwcGluZ3MiOiJBQUFBLE1BQU1BLE9BQU9DLG1CQUFPQSxDQUFDO0FBQ3JCLE1BQU1DLFNBQVNELG1CQUFPQSxDQUFDO0FBRXZCRSxPQUFPQyxPQUFPLEdBQUdKLEtBQUtLLEtBQUssQ0FBQ0MsV0FBVyxJQUFLLEVBQUNDLE1BQVFBLE9BQU9BLGVBQWVMLE9BQU9NLFNBQVEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaHJvbm9zLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL2hlbHBlcnMvaXNfa2V5X29iamVjdC5qcz9hMDBiIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHV0aWwgPSByZXF1aXJlKCd1dGlsJyk7XG5jb25zdCBjcnlwdG8gPSByZXF1aXJlKCdjcnlwdG8nKTtcblxubW9kdWxlLmV4cG9ydHMgPSB1dGlsLnR5cGVzLmlzS2V5T2JqZWN0IHx8ICgob2JqKSA9PiBvYmogJiYgb2JqIGluc3RhbmNlb2YgY3J5cHRvLktleU9iamVjdCk7XG4iXSwibmFtZXMiOlsidXRpbCIsInJlcXVpcmUiLCJjcnlwdG8iLCJtb2R1bGUiLCJleHBvcnRzIiwidHlwZXMiLCJpc0tleU9iamVjdCIsIm9iaiIsIktleU9iamVjdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/is_key_object.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js":
/*!*******************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/is_plain_object.js ***!
  \*******************************************************************/
/***/ ((module) => {

eval("module.exports = (a)=>!!a && a.constructor === Object;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaHJvbm9zLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL2hlbHBlcnMvaXNfcGxhaW5fb2JqZWN0LmpzP2UzZTgiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSAoYSkgPT4gISFhICYmIGEuY29uc3RydWN0b3IgPT09IE9iamVjdDtcbiJdLCJuYW1lcyI6WyJtb2R1bGUiLCJleHBvcnRzIiwiYSIsImNvbnN0cnVjdG9yIiwiT2JqZWN0Il0sIm1hcHBpbmdzIjoiQUFBQUEsT0FBT0MsT0FBTyxHQUFHLENBQUNDLElBQU0sQ0FBQyxDQUFDQSxLQUFLQSxFQUFFQyxXQUFXLEtBQUtDIiwiZmlsZSI6Iihyc2MpLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL2hlbHBlcnMvaXNfcGxhaW5fb2JqZWN0LmpzIiwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/issuer.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/issuer.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const objectHash = __webpack_require__(/*! object-hash */ \"(rsc)/./node_modules/object-hash/index.js\");\nconst LRU = __webpack_require__(/*! lru-cache */ \"(rsc)/./node_modules/lru-cache/index.js\");\nconst { RPError } = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst { assertIssuerConfiguration } = __webpack_require__(/*! ./assert */ \"(rsc)/./node_modules/openid-client/lib/helpers/assert.js\");\nconst KeyStore = __webpack_require__(/*! ./keystore */ \"(rsc)/./node_modules/openid-client/lib/helpers/keystore.js\");\nconst { keystores } = __webpack_require__(/*! ./weak_cache */ \"(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\");\nconst processResponse = __webpack_require__(/*! ./process_response */ \"(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\");\nconst request = __webpack_require__(/*! ./request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nconst inFlight = new WeakMap();\nconst caches = new WeakMap();\nconst lrus = (ctx)=>{\n    if (!caches.has(ctx)) {\n        caches.set(ctx, new LRU({\n            max: 100\n        }));\n    }\n    return caches.get(ctx);\n};\nasync function getKeyStore(reload = false) {\n    assertIssuerConfiguration(this, \"jwks_uri\");\n    const keystore = keystores.get(this);\n    const cache = lrus(this);\n    if (reload || !keystore) {\n        if (inFlight.has(this)) {\n            return inFlight.get(this);\n        }\n        cache.reset();\n        inFlight.set(this, (async ()=>{\n            const response = await request.call(this, {\n                method: \"GET\",\n                responseType: \"json\",\n                url: this.jwks_uri,\n                headers: {\n                    Accept: \"application/json, application/jwk-set+json\"\n                }\n            }).finally(()=>{\n                inFlight.delete(this);\n            });\n            const jwks = processResponse(response);\n            const joseKeyStore = KeyStore.fromJWKS(jwks, {\n                onlyPublic: true\n            });\n            cache.set(\"throttle\", true, 60 * 1000);\n            keystores.set(this, joseKeyStore);\n            return joseKeyStore;\n        })());\n        return inFlight.get(this);\n    }\n    return keystore;\n}\nasync function queryKeyStore({ kid, kty, alg, use }, { allowMulti = false } = {}) {\n    const cache = lrus(this);\n    const def = {\n        kid,\n        kty,\n        alg,\n        use\n    };\n    const defHash = objectHash(def, {\n        algorithm: \"sha256\",\n        ignoreUnknown: true,\n        unorderedArrays: true,\n        unorderedSets: true,\n        respectType: false\n    });\n    // refresh keystore on every unknown key but also only upto once every minute\n    const freshJwksUri = cache.get(defHash) || cache.get(\"throttle\");\n    const keystore = await getKeyStore.call(this, !freshJwksUri);\n    const keys = keystore.all(def);\n    delete def.use;\n    if (keys.length === 0) {\n        throw new RPError({\n            printf: [\n                \"no valid key found in issuer's jwks_uri for key parameters %j\",\n                def\n            ],\n            jwks: keystore\n        });\n    }\n    if (!allowMulti && keys.length > 1 && !kid) {\n        throw new RPError({\n            printf: [\n                \"multiple matching keys found in issuer's jwks_uri for key parameters %j, kid must be provided in this case\",\n                def\n            ],\n            jwks: keystore\n        });\n    }\n    cache.set(defHash, true);\n    return keys;\n}\nmodule.exports.queryKeyStore = queryKeyStore;\nmodule.exports.keystore = getKeyStore;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/issuer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/keystore.js":
/*!************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/keystore.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const jose = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/cjs/index.js\");\nconst clone = __webpack_require__(/*! ./deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst isPlainObject = __webpack_require__(/*! ./is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\nconst internal = Symbol();\nconst keyscore = (key, { alg, use })=>{\n    let score = 0;\n    if (alg && key.alg) {\n        score++;\n    }\n    if (use && key.use) {\n        score++;\n    }\n    return score;\n};\nfunction getKtyFromAlg(alg) {\n    switch(typeof alg === \"string\" && alg.slice(0, 2)){\n        case \"RS\":\n        case \"PS\":\n            return \"RSA\";\n        case \"ES\":\n            return \"EC\";\n        case \"Ed\":\n            return \"OKP\";\n        default:\n            return undefined;\n    }\n}\nfunction getAlgorithms(use, alg, kty, crv) {\n    // Ed25519, Ed448, and secp256k1 always have \"alg\"\n    // OKP always has \"use\"\n    if (alg) {\n        return new Set([\n            alg\n        ]);\n    }\n    switch(kty){\n        case \"EC\":\n            {\n                let algs = [];\n                if (use === \"enc\" || use === undefined) {\n                    algs = algs.concat([\n                        \"ECDH-ES\",\n                        \"ECDH-ES+A128KW\",\n                        \"ECDH-ES+A192KW\",\n                        \"ECDH-ES+A256KW\"\n                    ]);\n                }\n                if (use === \"sig\" || use === undefined) {\n                    switch(crv){\n                        case \"P-256\":\n                        case \"P-384\":\n                            algs = algs.concat([\n                                `ES${crv.slice(-3)}`\n                            ]);\n                            break;\n                        case \"P-521\":\n                            algs = algs.concat([\n                                \"ES512\"\n                            ]);\n                            break;\n                        case \"secp256k1\":\n                            if (jose.cryptoRuntime === \"node:crypto\") {\n                                algs = algs.concat([\n                                    \"ES256K\"\n                                ]);\n                            }\n                            break;\n                    }\n                }\n                return new Set(algs);\n            }\n        case \"OKP\":\n            {\n                return new Set([\n                    \"ECDH-ES\",\n                    \"ECDH-ES+A128KW\",\n                    \"ECDH-ES+A192KW\",\n                    \"ECDH-ES+A256KW\"\n                ]);\n            }\n        case \"RSA\":\n            {\n                let algs = [];\n                if (use === \"enc\" || use === undefined) {\n                    algs = algs.concat([\n                        \"RSA-OAEP\",\n                        \"RSA-OAEP-256\",\n                        \"RSA-OAEP-384\",\n                        \"RSA-OAEP-512\"\n                    ]);\n                    if (jose.cryptoRuntime === \"node:crypto\") {\n                        algs = algs.concat([\n                            \"RSA1_5\"\n                        ]);\n                    }\n                }\n                if (use === \"sig\" || use === undefined) {\n                    algs = algs.concat([\n                        \"PS256\",\n                        \"PS384\",\n                        \"PS512\",\n                        \"RS256\",\n                        \"RS384\",\n                        \"RS512\"\n                    ]);\n                }\n                return new Set(algs);\n            }\n        default:\n            throw new Error(\"unreachable\");\n    }\n}\nmodule.exports = class KeyStore {\n    #keys;\n    constructor(i, keys){\n        if (i !== internal) throw new Error(\"invalid constructor call\");\n        this.#keys = keys;\n    }\n    toJWKS() {\n        return {\n            keys: this.map(({ jwk: { d, p, q, dp, dq, qi, ...jwk } })=>jwk)\n        };\n    }\n    all({ alg, kid, use } = {}) {\n        if (!use || !alg) {\n            throw new Error();\n        }\n        const kty = getKtyFromAlg(alg);\n        const search = {\n            alg,\n            use\n        };\n        return this.filter((key)=>{\n            let candidate = true;\n            if (candidate && kty !== undefined && key.jwk.kty !== kty) {\n                candidate = false;\n            }\n            if (candidate && kid !== undefined && key.jwk.kid !== kid) {\n                candidate = false;\n            }\n            if (candidate && use !== undefined && key.jwk.use !== undefined && key.jwk.use !== use) {\n                candidate = false;\n            }\n            if (candidate && key.jwk.alg && key.jwk.alg !== alg) {\n                candidate = false;\n            } else if (!key.algorithms.has(alg)) {\n                candidate = false;\n            }\n            return candidate;\n        }).sort((first, second)=>keyscore(second, search) - keyscore(first, search));\n    }\n    get(...args) {\n        return this.all(...args)[0];\n    }\n    static async fromJWKS(jwks, { onlyPublic = false, onlyPrivate = false } = {}) {\n        if (!isPlainObject(jwks) || !Array.isArray(jwks.keys) || jwks.keys.some((k)=>!isPlainObject(k) || !(\"kty\" in k))) {\n            throw new TypeError(\"jwks must be a JSON Web Key Set formatted object\");\n        }\n        const keys = [];\n        for (let jwk of jwks.keys){\n            jwk = clone(jwk);\n            const { kty, kid, crv } = jwk;\n            let { alg, use } = jwk;\n            if (typeof kty !== \"string\" || !kty) {\n                continue;\n            }\n            if (use !== undefined && use !== \"sig\" && use !== \"enc\") {\n                continue;\n            }\n            if (typeof alg !== \"string\" && alg !== undefined) {\n                continue;\n            }\n            if (typeof kid !== \"string\" && kid !== undefined) {\n                continue;\n            }\n            if (kty === \"EC\" && use === \"sig\") {\n                switch(crv){\n                    case \"P-256\":\n                        alg = \"ES256\";\n                        break;\n                    case \"P-384\":\n                        alg = \"ES384\";\n                        break;\n                    case \"P-521\":\n                        alg = \"ES512\";\n                        break;\n                    default:\n                        break;\n                }\n            }\n            if (crv === \"secp256k1\") {\n                use = \"sig\";\n                alg = \"ES256K\";\n            }\n            if (kty === \"OKP\") {\n                switch(crv){\n                    case \"Ed25519\":\n                    case \"Ed448\":\n                        use = \"sig\";\n                        alg = \"EdDSA\";\n                        break;\n                    case \"X25519\":\n                    case \"X448\":\n                        use = \"enc\";\n                        break;\n                    default:\n                        break;\n                }\n            }\n            if (alg && !use) {\n                switch(true){\n                    case alg.startsWith(\"ECDH\"):\n                        use = \"enc\";\n                        break;\n                    case alg.startsWith(\"RSA\"):\n                        use = \"enc\";\n                        break;\n                    default:\n                        break;\n                }\n            }\n            if (onlyPrivate && (jwk.kty === \"oct\" || !jwk.d)) {\n                throw new Error(\"jwks must only contain private keys\");\n            }\n            if (onlyPublic && (jwk.d || jwk.k)) {\n                continue;\n            }\n            keys.push({\n                jwk: {\n                    ...jwk,\n                    alg,\n                    use\n                },\n                async keyObject (alg) {\n                    if (this[alg]) {\n                        return this[alg];\n                    }\n                    const keyObject = await jose.importJWK(this.jwk, alg);\n                    this[alg] = keyObject;\n                    return keyObject;\n                },\n                get algorithms () {\n                    Object.defineProperty(this, \"algorithms\", {\n                        value: getAlgorithms(this.jwk.use, this.jwk.alg, this.jwk.kty, this.jwk.crv),\n                        enumerable: true,\n                        configurable: false\n                    });\n                    return this.algorithms;\n                }\n            });\n        }\n        return new this(internal, keys);\n    }\n    filter(...args) {\n        return this.#keys.filter(...args);\n    }\n    find(...args) {\n        return this.#keys.find(...args);\n    }\n    every(...args) {\n        return this.#keys.every(...args);\n    }\n    some(...args) {\n        return this.#keys.some(...args);\n    }\n    map(...args) {\n        return this.#keys.map(...args);\n    }\n    forEach(...args) {\n        return this.#keys.forEach(...args);\n    }\n    reduce(...args) {\n        return this.#keys.reduce(...args);\n    }\n    sort(...args) {\n        return this.#keys.sort(...args);\n    }\n    *[Symbol.iterator]() {\n        for (const key of this.#keys){\n            yield key;\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/keystore.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/merge.js":
/*!*********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/merge.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const isPlainObject = __webpack_require__(/*! ./is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\nfunction merge(target, ...sources) {\n    for (const source of sources){\n        if (!isPlainObject(source)) {\n            continue;\n        }\n        for (const [key, value] of Object.entries(source)){\n            /* istanbul ignore if */ if (key === \"__proto__\" || key === \"constructor\") {\n                continue;\n            }\n            if (isPlainObject(target[key]) && isPlainObject(value)) {\n                target[key] = merge(target[key], value);\n            } else if (typeof value !== \"undefined\") {\n                target[key] = value;\n            }\n        }\n    }\n    return target;\n}\nmodule.exports = merge;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9tZXJnZS5qcyIsIm1hcHBpbmdzIjoiQUFBQSxNQUFNQSxnQkFBZ0JDLG1CQUFPQSxDQUFDO0FBRTlCLFNBQVNDLE1BQU1DLE1BQU0sRUFBRSxHQUFHQyxPQUFPO0lBQy9CLEtBQUssTUFBTUMsVUFBVUQsUUFBUztRQUM1QixJQUFJLENBQUNKLGNBQWNLLFNBQVM7WUFDMUI7UUFDRjtRQUNBLEtBQUssTUFBTSxDQUFDQyxLQUFLQyxNQUFNLElBQUlDLE9BQU9DLE9BQU8sQ0FBQ0osUUFBUztZQUNqRCxzQkFBc0IsR0FDdEIsSUFBSUMsUUFBUSxlQUFlQSxRQUFRLGVBQWU7Z0JBQ2hEO1lBQ0Y7WUFDQSxJQUFJTixjQUFjRyxNQUFNLENBQUNHLElBQUksS0FBS04sY0FBY08sUUFBUTtnQkFDdERKLE1BQU0sQ0FBQ0csSUFBSSxHQUFHSixNQUFNQyxNQUFNLENBQUNHLElBQUksRUFBRUM7WUFDbkMsT0FBTyxJQUFJLE9BQU9BLFVBQVUsYUFBYTtnQkFDdkNKLE1BQU0sQ0FBQ0csSUFBSSxHQUFHQztZQUNoQjtRQUNGO0lBQ0Y7SUFFQSxPQUFPSjtBQUNUO0FBRUFPLE9BQU9DLE9BQU8sR0FBR1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaHJvbm9zLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL2hlbHBlcnMvbWVyZ2UuanM/YTU4ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBpc1BsYWluT2JqZWN0ID0gcmVxdWlyZSgnLi9pc19wbGFpbl9vYmplY3QnKTtcblxuZnVuY3Rpb24gbWVyZ2UodGFyZ2V0LCAuLi5zb3VyY2VzKSB7XG4gIGZvciAoY29uc3Qgc291cmNlIG9mIHNvdXJjZXMpIHtcbiAgICBpZiAoIWlzUGxhaW5PYmplY3Qoc291cmNlKSkge1xuICAgICAgY29udGludWU7XG4gICAgfVxuICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKHNvdXJjZSkpIHtcbiAgICAgIC8qIGlzdGFuYnVsIGlnbm9yZSBpZiAqL1xuICAgICAgaWYgKGtleSA9PT0gJ19fcHJvdG9fXycgfHwga2V5ID09PSAnY29uc3RydWN0b3InKSB7XG4gICAgICAgIGNvbnRpbnVlO1xuICAgICAgfVxuICAgICAgaWYgKGlzUGxhaW5PYmplY3QodGFyZ2V0W2tleV0pICYmIGlzUGxhaW5PYmplY3QodmFsdWUpKSB7XG4gICAgICAgIHRhcmdldFtrZXldID0gbWVyZ2UodGFyZ2V0W2tleV0sIHZhbHVlKTtcbiAgICAgIH0gZWxzZSBpZiAodHlwZW9mIHZhbHVlICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICB0YXJnZXRba2V5XSA9IHZhbHVlO1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiB0YXJnZXQ7XG59XG5cbm1vZHVsZS5leHBvcnRzID0gbWVyZ2U7XG4iXSwibmFtZXMiOlsiaXNQbGFpbk9iamVjdCIsInJlcXVpcmUiLCJtZXJnZSIsInRhcmdldCIsInNvdXJjZXMiLCJzb3VyY2UiLCJrZXkiLCJ2YWx1ZSIsIk9iamVjdCIsImVudHJpZXMiLCJtb2R1bGUiLCJleHBvcnRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/merge.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/pick.js":
/*!********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/pick.js ***!
  \********************************************************/
/***/ ((module) => {

eval("module.exports = function pick(object, ...paths) {\n    const obj = {};\n    for (const path of paths){\n        if (object[path] !== undefined) {\n            obj[path] = object[path];\n        }\n    }\n    return obj;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaHJvbm9zLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL2hlbHBlcnMvcGljay5qcz82ODY3Il0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gcGljayhvYmplY3QsIC4uLnBhdGhzKSB7XG4gIGNvbnN0IG9iaiA9IHt9O1xuICBmb3IgKGNvbnN0IHBhdGggb2YgcGF0aHMpIHtcbiAgICBpZiAob2JqZWN0W3BhdGhdICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIG9ialtwYXRoXSA9IG9iamVjdFtwYXRoXTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIG9iajtcbn07XG4iXSwibmFtZXMiOlsibW9kdWxlIiwiZXhwb3J0cyIsInBpY2siLCJvYmplY3QiLCJwYXRocyIsIm9iaiIsInBhdGgiLCJ1bmRlZmluZWQiXSwibWFwcGluZ3MiOiJBQUFBQSxPQUFPQyxPQUFPLEdBQUcsU0FBU0MsS0FBS0MsTUFBTSxFQUFFLEdBQUdDLEtBQUs7SUFDN0MsTUFBTUMsTUFBTSxDQUFDO0lBQ2IsS0FBSyxNQUFNQyxRQUFRRixNQUFPO1FBQ3hCLElBQUlELE1BQU0sQ0FBQ0csS0FBSyxLQUFLQyxXQUFXO1lBQzlCRixHQUFHLENBQUNDLEtBQUssR0FBR0gsTUFBTSxDQUFDRyxLQUFLO1FBQzFCO0lBQ0Y7SUFDQSxPQUFPRDtBQUNUIiwiZmlsZSI6Iihyc2MpLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL2hlbHBlcnMvcGljay5qcyIsInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/pick.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/process_response.js":
/*!********************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/process_response.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { STATUS_CODES } = __webpack_require__(/*! http */ \"http\");\nconst { format } = __webpack_require__(/*! util */ \"util\");\nconst { OPError } = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst parseWwwAuthenticate = __webpack_require__(/*! ./www_authenticate_parser */ \"(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js\");\nconst throwAuthenticateErrors = (response)=>{\n    const params = parseWwwAuthenticate(response.headers[\"www-authenticate\"]);\n    if (params.error) {\n        throw new OPError(params, response);\n    }\n};\nconst isStandardBodyError = (response)=>{\n    let result = false;\n    try {\n        let jsonbody;\n        if (typeof response.body !== \"object\" || Buffer.isBuffer(response.body)) {\n            jsonbody = JSON.parse(response.body);\n        } else {\n            jsonbody = response.body;\n        }\n        result = typeof jsonbody.error === \"string\" && jsonbody.error.length;\n        if (result) Object.defineProperty(response, \"body\", {\n            value: jsonbody,\n            configurable: true\n        });\n    } catch (err) {}\n    return result;\n};\nfunction processResponse(response, { statusCode = 200, body = true, bearer = false } = {}) {\n    if (response.statusCode !== statusCode) {\n        if (bearer) {\n            throwAuthenticateErrors(response);\n        }\n        if (isStandardBodyError(response)) {\n            throw new OPError(response.body, response);\n        }\n        throw new OPError({\n            error: format(\"expected %i %s, got: %i %s\", statusCode, STATUS_CODES[statusCode], response.statusCode, STATUS_CODES[response.statusCode])\n        }, response);\n    }\n    if (body && !response.body) {\n        throw new OPError({\n            error: format(\"expected %i %s with body but no body was returned\", statusCode, STATUS_CODES[statusCode])\n        }, response);\n    }\n    return response.body;\n}\nmodule.exports = processResponse;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/request.js":
/*!***********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/request.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const assert = __webpack_require__(/*! assert */ \"assert\");\nconst querystring = __webpack_require__(/*! querystring */ \"querystring\");\nconst http = __webpack_require__(/*! http */ \"http\");\nconst https = __webpack_require__(/*! https */ \"https\");\nconst { once } = __webpack_require__(/*! events */ \"events\");\nconst { URL } = __webpack_require__(/*! url */ \"url\");\nconst LRU = __webpack_require__(/*! lru-cache */ \"(rsc)/./node_modules/lru-cache/index.js\");\nconst pkg = __webpack_require__(/*! ../../package.json */ \"(rsc)/./node_modules/openid-client/package.json\");\nconst { RPError } = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst pick = __webpack_require__(/*! ./pick */ \"(rsc)/./node_modules/openid-client/lib/helpers/pick.js\");\nconst { deep: defaultsDeep } = __webpack_require__(/*! ./defaults */ \"(rsc)/./node_modules/openid-client/lib/helpers/defaults.js\");\nconst { HTTP_OPTIONS } = __webpack_require__(/*! ./consts */ \"(rsc)/./node_modules/openid-client/lib/helpers/consts.js\");\nlet DEFAULT_HTTP_OPTIONS;\nconst NQCHAR = /^[\\x21\\x23-\\x5B\\x5D-\\x7E]+$/;\nconst allowed = [\n    \"agent\",\n    \"ca\",\n    \"cert\",\n    \"crl\",\n    \"headers\",\n    \"key\",\n    \"lookup\",\n    \"passphrase\",\n    \"pfx\",\n    \"timeout\"\n];\nconst setDefaults = (props, options)=>{\n    DEFAULT_HTTP_OPTIONS = defaultsDeep({}, props.length ? pick(options, ...props) : options, DEFAULT_HTTP_OPTIONS);\n};\nsetDefaults([], {\n    headers: {\n        \"User-Agent\": `${pkg.name}/${pkg.version} (${pkg.homepage})`,\n        \"Accept-Encoding\": \"identity\"\n    },\n    timeout: 3500\n});\nfunction send(req, body, contentType) {\n    if (contentType) {\n        req.removeHeader(\"content-type\");\n        req.setHeader(\"content-type\", contentType);\n    }\n    if (body) {\n        req.removeHeader(\"content-length\");\n        req.setHeader(\"content-length\", Buffer.byteLength(body));\n        req.write(body);\n    }\n    req.end();\n}\nconst nonces = new LRU({\n    max: 100\n});\nmodule.exports = async function request(options, { accessToken, mTLS = false, DPoP } = {}) {\n    let url;\n    try {\n        url = new URL(options.url);\n        delete options.url;\n        assert(/^(https?:)$/.test(url.protocol));\n    } catch (err) {\n        throw new TypeError(\"only valid absolute URLs can be requested\");\n    }\n    const optsFn = this[HTTP_OPTIONS];\n    let opts = options;\n    const nonceKey = `${url.origin}${url.pathname}`;\n    if (DPoP && \"dpopProof\" in this) {\n        opts.headers = opts.headers || {};\n        opts.headers.DPoP = await this.dpopProof({\n            htu: `${url.origin}${url.pathname}`,\n            htm: options.method || \"GET\",\n            nonce: nonces.get(nonceKey)\n        }, DPoP, accessToken);\n    }\n    let userOptions;\n    if (optsFn) {\n        userOptions = pick(optsFn.call(this, url, defaultsDeep({}, opts, DEFAULT_HTTP_OPTIONS)), ...allowed);\n    }\n    opts = defaultsDeep({}, userOptions, opts, DEFAULT_HTTP_OPTIONS);\n    if (mTLS && !opts.pfx && !(opts.key && opts.cert)) {\n        throw new TypeError(\"mutual-TLS certificate and key not set\");\n    }\n    if (opts.searchParams) {\n        for (const [key, value] of Object.entries(opts.searchParams)){\n            url.searchParams.delete(key);\n            url.searchParams.set(key, value);\n        }\n    }\n    let responseType;\n    let form;\n    let json;\n    let body;\n    ({ form, responseType, json, body, ...opts } = opts);\n    for (const [key, value] of Object.entries(opts.headers || {})){\n        if (value === undefined) {\n            delete opts.headers[key];\n        }\n    }\n    let response;\n    const req = (url.protocol === \"https:\" ? https.request : http.request)(url.href, opts);\n    return (async ()=>{\n        if (json) {\n            send(req, JSON.stringify(json), \"application/json\");\n        } else if (form) {\n            send(req, querystring.stringify(form), \"application/x-www-form-urlencoded\");\n        } else if (body) {\n            send(req, body);\n        } else {\n            send(req);\n        }\n        [response] = await Promise.race([\n            once(req, \"response\"),\n            once(req, \"timeout\")\n        ]);\n        // timeout reached\n        if (!response) {\n            req.destroy();\n            throw new RPError(`outgoing request timed out after ${opts.timeout}ms`);\n        }\n        const parts = [];\n        for await (const part of response){\n            parts.push(part);\n        }\n        if (parts.length) {\n            switch(responseType){\n                case \"json\":\n                    {\n                        Object.defineProperty(response, \"body\", {\n                            get () {\n                                let value = Buffer.concat(parts);\n                                try {\n                                    value = JSON.parse(value);\n                                } catch (err) {\n                                    Object.defineProperty(err, \"response\", {\n                                        value: response\n                                    });\n                                    throw err;\n                                } finally{\n                                    Object.defineProperty(response, \"body\", {\n                                        value,\n                                        configurable: true\n                                    });\n                                }\n                                return value;\n                            },\n                            configurable: true\n                        });\n                        break;\n                    }\n                case undefined:\n                case \"buffer\":\n                    {\n                        Object.defineProperty(response, \"body\", {\n                            get () {\n                                const value = Buffer.concat(parts);\n                                Object.defineProperty(response, \"body\", {\n                                    value,\n                                    configurable: true\n                                });\n                                return value;\n                            },\n                            configurable: true\n                        });\n                        break;\n                    }\n                default:\n                    throw new TypeError(\"unsupported responseType request option\");\n            }\n        }\n        return response;\n    })().catch((err)=>{\n        if (response) Object.defineProperty(err, \"response\", {\n            value: response\n        });\n        throw err;\n    }).finally(()=>{\n        const dpopNonce = response && response.headers[\"dpop-nonce\"];\n        if (dpopNonce && NQCHAR.test(dpopNonce)) {\n            nonces.set(nonceKey, dpopNonce);\n        }\n    });\n};\nmodule.exports.setDefaults = setDefaults.bind(undefined, allowed);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/request.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js":
/*!******************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/unix_timestamp.js ***!
  \******************************************************************/
/***/ ((module) => {

eval("module.exports = ()=>Math.floor(Date.now() / 1000);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaHJvbm9zLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL2hlbHBlcnMvdW5peF90aW1lc3RhbXAuanM/NGU3ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9ICgpID0+IE1hdGguZmxvb3IoRGF0ZS5ub3coKSAvIDEwMDApO1xuIl0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiLCJNYXRoIiwiZmxvb3IiLCJEYXRlIiwibm93Il0sIm1hcHBpbmdzIjoiQUFBQUEsT0FBT0MsT0FBTyxHQUFHLElBQU1DLEtBQUtDLEtBQUssQ0FBQ0MsS0FBS0MsR0FBRyxLQUFLIiwiZmlsZSI6Iihyc2MpLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL2hlbHBlcnMvdW5peF90aW1lc3RhbXAuanMiLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/weak_cache.js ***!
  \**************************************************************/
/***/ ((module) => {

eval("module.exports.keystores = new WeakMap();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy93ZWFrX2NhY2hlLmpzIiwibWFwcGluZ3MiOiJBQUFBQSx3QkFBd0IsR0FBRyxJQUFJRyIsInNvdXJjZXMiOlsid2VicGFjazovL2Nocm9ub3MtcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy93ZWFrX2NhY2hlLmpzP2M5MzAiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMua2V5c3RvcmVzID0gbmV3IFdlYWtNYXAoKTtcbiJdLCJuYW1lcyI6WyJtb2R1bGUiLCJleHBvcnRzIiwia2V5c3RvcmVzIiwiV2Vha01hcCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/webfinger_normalize.js":
/*!***********************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/webfinger_normalize.js ***!
  \***********************************************************************/
/***/ ((module) => {

eval("// Credit: https://github.com/rohe/pyoidc/blob/master/src/oic/utils/webfinger.py\n// -- Normalization --\n// A string of any other type is interpreted as a URI either the form of scheme\n// \"://\" authority path-abempty [ \"?\" query ] [ \"#\" fragment ] or authority\n// path-abempty [ \"?\" query ] [ \"#\" fragment ] per RFC 3986 [RFC3986] and is\n// normalized according to the following rules:\n//\n// If the user input Identifier does not have an RFC 3986 [RFC3986] scheme\n// portion, the string is interpreted as [userinfo \"@\"] host [\":\" port]\n// path-abempty [ \"?\" query ] [ \"#\" fragment ] per RFC 3986 [RFC3986].\n// If the userinfo component is present and all of the path component, query\n// component, and port component are empty, the acct scheme is assumed. In this\n// case, the normalized URI is formed by prefixing acct: to the string as the\n// scheme. Per the 'acct' URI Scheme [I‑D.ietf‑appsawg‑acct‑uri], if there is an\n// at-sign character ('@') in the userinfo component, it needs to be\n// percent-encoded as described in RFC 3986 [RFC3986].\n// For all other inputs without a scheme portion, the https scheme is assumed,\n// and the normalized URI is formed by prefixing https:// to the string as the\n// scheme.\n// If the resulting URI contains a fragment portion, it MUST be stripped off\n// together with the fragment delimiter character \"#\".\n// The WebFinger [I‑D.ietf‑appsawg‑webfinger] Resource in this case is the\n// resulting URI, and the WebFinger Host is the authority component.\n//\n// Note: Since the definition of authority in RFC 3986 [RFC3986] is\n// [ userinfo \"@\" ] host [ \":\" port ], it is legal to have a user input\n// identifier like userinfo@host:port, e.g., <EMAIL>:8080.\nconst PORT = /^\\d+$/;\nfunction hasScheme(input) {\n    if (input.includes(\"://\")) return true;\n    const authority = input.replace(/(\\/|\\?)/g, \"#\").split(\"#\")[0];\n    if (authority.includes(\":\")) {\n        const index = authority.indexOf(\":\");\n        const hostOrPort = authority.slice(index + 1);\n        if (!PORT.test(hostOrPort)) {\n            return true;\n        }\n    }\n    return false;\n}\nfunction acctSchemeAssumed(input) {\n    if (!input.includes(\"@\")) return false;\n    const parts = input.split(\"@\");\n    const host = parts[parts.length - 1];\n    return !(host.includes(\":\") || host.includes(\"/\") || host.includes(\"?\"));\n}\nfunction normalize(input) {\n    if (typeof input !== \"string\") {\n        throw new TypeError(\"input must be a string\");\n    }\n    let output;\n    if (hasScheme(input)) {\n        output = input;\n    } else if (acctSchemeAssumed(input)) {\n        output = `acct:${input}`;\n    } else {\n        output = `https://${input}`;\n    }\n    return output.split(\"#\")[0];\n}\nmodule.exports = normalize;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/webfinger_normalize.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js":
/*!***************************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/www_authenticate_parser.js ***!
  \***************************************************************************/
/***/ ((module) => {

eval("const REGEXP = /(\\w+)=(\"[^\"]*\")/g;\nmodule.exports = (wwwAuthenticate)=>{\n    const params = {};\n    try {\n        while(REGEXP.exec(wwwAuthenticate) !== null){\n            if (RegExp.$1 && RegExp.$2) {\n                params[RegExp.$1] = RegExp.$2.slice(1, -1);\n            }\n        }\n    } catch (err) {}\n    return params;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaHJvbm9zLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL2hlbHBlcnMvd3d3X2F1dGhlbnRpY2F0ZV9wYXJzZXIuanM/ZjAwZCJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBSRUdFWFAgPSAvKFxcdyspPShcIlteXCJdKlwiKS9nO1xuXG5tb2R1bGUuZXhwb3J0cyA9ICh3d3dBdXRoZW50aWNhdGUpID0+IHtcbiAgY29uc3QgcGFyYW1zID0ge307XG4gIHRyeSB7XG4gICAgd2hpbGUgKFJFR0VYUC5leGVjKHd3d0F1dGhlbnRpY2F0ZSkgIT09IG51bGwpIHtcbiAgICAgIGlmIChSZWdFeHAuJDEgJiYgUmVnRXhwLiQyKSB7XG4gICAgICAgIHBhcmFtc1tSZWdFeHAuJDFdID0gUmVnRXhwLiQyLnNsaWNlKDEsIC0xKTtcbiAgICAgIH1cbiAgICB9XG4gIH0gY2F0Y2ggKGVycikge31cblxuICByZXR1cm4gcGFyYW1zO1xufTtcbiJdLCJuYW1lcyI6WyJSRUdFWFAiLCJtb2R1bGUiLCJleHBvcnRzIiwid3d3QXV0aGVudGljYXRlIiwicGFyYW1zIiwiZXhlYyIsIlJlZ0V4cCIsIiQxIiwiJDIiLCJzbGljZSIsImVyciJdLCJtYXBwaW5ncyI6IkFBQUEsTUFBTUEsU0FBUztBQUVmQyxPQUFPQyxPQUFPLEdBQUcsQ0FBQ0M7SUFDaEIsTUFBTUMsU0FBUyxDQUFDO0lBQ2hCLElBQUk7UUFDRixNQUFPSixPQUFPSyxJQUFJLENBQUNGLHFCQUFxQixLQUFNO1lBQzVDLElBQUlHLE9BQU9DLEVBQUUsSUFBSUQsT0FBT0UsRUFBRSxFQUFFO2dCQUMxQkosTUFBTSxDQUFDRSxPQUFPQyxFQUFFLENBQUMsR0FBR0QsT0FBT0UsRUFBRSxDQUFDQyxLQUFLLENBQUMsR0FBRyxDQUFDO1lBQzFDO1FBQ0Y7SUFDRixFQUFFLE9BQU9DLEtBQUssQ0FBQztJQUVmLE9BQU9OO0FBQ1QiLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy93d3dfYXV0aGVudGljYXRlX3BhcnNlci5qcyIsInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/index.js":
/*!*************************************************!*\
  !*** ./node_modules/openid-client/lib/index.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const Issuer = __webpack_require__(/*! ./issuer */ \"(rsc)/./node_modules/openid-client/lib/issuer.js\");\nconst { OPError, RPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst Strategy = __webpack_require__(/*! ./passport_strategy */ \"(rsc)/./node_modules/openid-client/lib/passport_strategy.js\");\nconst TokenSet = __webpack_require__(/*! ./token_set */ \"(rsc)/./node_modules/openid-client/lib/token_set.js\");\nconst { CLOCK_TOLERANCE, HTTP_OPTIONS } = __webpack_require__(/*! ./helpers/consts */ \"(rsc)/./node_modules/openid-client/lib/helpers/consts.js\");\nconst generators = __webpack_require__(/*! ./helpers/generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst { setDefaults } = __webpack_require__(/*! ./helpers/request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nmodule.exports = {\n    Issuer,\n    Strategy,\n    TokenSet,\n    errors: {\n        OPError,\n        RPError\n    },\n    custom: {\n        setHttpOptionsDefaults: setDefaults,\n        http_options: HTTP_OPTIONS,\n        clock_tolerance: CLOCK_TOLERANCE\n    },\n    generators\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUEsTUFBTUEsU0FBU0MsbUJBQU9BLENBQUM7QUFDdkIsTUFBTSxFQUFFQyxPQUFPLEVBQUVDLE9BQU8sRUFBRSxHQUFHRixtQkFBT0EsQ0FBQztBQUNyQyxNQUFNRyxXQUFXSCxtQkFBT0EsQ0FBQztBQUN6QixNQUFNSSxXQUFXSixtQkFBT0EsQ0FBQztBQUN6QixNQUFNLEVBQUVLLGVBQWUsRUFBRUMsWUFBWSxFQUFFLEdBQUdOLG1CQUFPQSxDQUFDO0FBQ2xELE1BQU1PLGFBQWFQLG1CQUFPQSxDQUFDO0FBQzNCLE1BQU0sRUFBRVEsV0FBVyxFQUFFLEdBQUdSLG1CQUFPQSxDQUFDO0FBRWhDUyxPQUFPQyxPQUFPLEdBQUc7SUFDZlg7SUFDQUk7SUFDQUM7SUFDQU8sUUFBUTtRQUNOVjtRQUNBQztJQUNGO0lBQ0FVLFFBQVE7UUFDTkMsd0JBQXdCTDtRQUN4Qk0sY0FBY1I7UUFDZFMsaUJBQWlCVjtJQUNuQjtJQUNBRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2hyb25vcy1wbGF0Zm9ybS8uL25vZGVfbW9kdWxlcy9vcGVuaWQtY2xpZW50L2xpYi9pbmRleC5qcz9jZjkxIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IElzc3VlciA9IHJlcXVpcmUoJy4vaXNzdWVyJyk7XG5jb25zdCB7IE9QRXJyb3IsIFJQRXJyb3IgfSA9IHJlcXVpcmUoJy4vZXJyb3JzJyk7XG5jb25zdCBTdHJhdGVneSA9IHJlcXVpcmUoJy4vcGFzc3BvcnRfc3RyYXRlZ3knKTtcbmNvbnN0IFRva2VuU2V0ID0gcmVxdWlyZSgnLi90b2tlbl9zZXQnKTtcbmNvbnN0IHsgQ0xPQ0tfVE9MRVJBTkNFLCBIVFRQX09QVElPTlMgfSA9IHJlcXVpcmUoJy4vaGVscGVycy9jb25zdHMnKTtcbmNvbnN0IGdlbmVyYXRvcnMgPSByZXF1aXJlKCcuL2hlbHBlcnMvZ2VuZXJhdG9ycycpO1xuY29uc3QgeyBzZXREZWZhdWx0cyB9ID0gcmVxdWlyZSgnLi9oZWxwZXJzL3JlcXVlc3QnKTtcblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIElzc3VlcixcbiAgU3RyYXRlZ3ksXG4gIFRva2VuU2V0LFxuICBlcnJvcnM6IHtcbiAgICBPUEVycm9yLFxuICAgIFJQRXJyb3IsXG4gIH0sXG4gIGN1c3RvbToge1xuICAgIHNldEh0dHBPcHRpb25zRGVmYXVsdHM6IHNldERlZmF1bHRzLFxuICAgIGh0dHBfb3B0aW9uczogSFRUUF9PUFRJT05TLFxuICAgIGNsb2NrX3RvbGVyYW5jZTogQ0xPQ0tfVE9MRVJBTkNFLFxuICB9LFxuICBnZW5lcmF0b3JzLFxufTtcbiJdLCJuYW1lcyI6WyJJc3N1ZXIiLCJyZXF1aXJlIiwiT1BFcnJvciIsIlJQRXJyb3IiLCJTdHJhdGVneSIsIlRva2VuU2V0IiwiQ0xPQ0tfVE9MRVJBTkNFIiwiSFRUUF9PUFRJT05TIiwiZ2VuZXJhdG9ycyIsInNldERlZmF1bHRzIiwibW9kdWxlIiwiZXhwb3J0cyIsImVycm9ycyIsImN1c3RvbSIsInNldEh0dHBPcHRpb25zRGVmYXVsdHMiLCJodHRwX29wdGlvbnMiLCJjbG9ja190b2xlcmFuY2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/issuer.js":
/*!**************************************************!*\
  !*** ./node_modules/openid-client/lib/issuer.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { inspect } = __webpack_require__(/*! util */ \"util\");\nconst url = __webpack_require__(/*! url */ \"url\");\nconst { RPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst getClient = __webpack_require__(/*! ./client */ \"(rsc)/./node_modules/openid-client/lib/client.js\");\nconst registry = __webpack_require__(/*! ./issuer_registry */ \"(rsc)/./node_modules/openid-client/lib/issuer_registry.js\");\nconst processResponse = __webpack_require__(/*! ./helpers/process_response */ \"(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\");\nconst webfingerNormalize = __webpack_require__(/*! ./helpers/webfinger_normalize */ \"(rsc)/./node_modules/openid-client/lib/helpers/webfinger_normalize.js\");\nconst request = __webpack_require__(/*! ./helpers/request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nconst clone = __webpack_require__(/*! ./helpers/deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst { keystore } = __webpack_require__(/*! ./helpers/issuer */ \"(rsc)/./node_modules/openid-client/lib/helpers/issuer.js\");\nconst AAD_MULTITENANT_DISCOVERY = [\n    \"https://login.microsoftonline.com/common/.well-known/openid-configuration\",\n    \"https://login.microsoftonline.com/common/v2.0/.well-known/openid-configuration\",\n    \"https://login.microsoftonline.com/organizations/v2.0/.well-known/openid-configuration\",\n    \"https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration\"\n];\nconst AAD_MULTITENANT = Symbol();\nconst ISSUER_DEFAULTS = {\n    claim_types_supported: [\n        \"normal\"\n    ],\n    claims_parameter_supported: false,\n    grant_types_supported: [\n        \"authorization_code\",\n        \"implicit\"\n    ],\n    request_parameter_supported: false,\n    request_uri_parameter_supported: true,\n    require_request_uri_registration: false,\n    response_modes_supported: [\n        \"query\",\n        \"fragment\"\n    ],\n    token_endpoint_auth_methods_supported: [\n        \"client_secret_basic\"\n    ]\n};\nclass Issuer {\n    #metadata;\n    constructor(meta = {}){\n        const aadIssValidation = meta[AAD_MULTITENANT];\n        delete meta[AAD_MULTITENANT];\n        [\n            \"introspection\",\n            \"revocation\"\n        ].forEach((endpoint)=>{\n            // if intro/revocation endpoint auth specific meta is missing use the token ones if they\n            // are defined\n            if (meta[`${endpoint}_endpoint`] && meta[`${endpoint}_endpoint_auth_methods_supported`] === undefined && meta[`${endpoint}_endpoint_auth_signing_alg_values_supported`] === undefined) {\n                if (meta.token_endpoint_auth_methods_supported) {\n                    meta[`${endpoint}_endpoint_auth_methods_supported`] = meta.token_endpoint_auth_methods_supported;\n                }\n                if (meta.token_endpoint_auth_signing_alg_values_supported) {\n                    meta[`${endpoint}_endpoint_auth_signing_alg_values_supported`] = meta.token_endpoint_auth_signing_alg_values_supported;\n                }\n            }\n        });\n        this.#metadata = new Map();\n        Object.entries(meta).forEach(([key, value])=>{\n            this.#metadata.set(key, value);\n            if (!this[key]) {\n                Object.defineProperty(this, key, {\n                    get () {\n                        return this.#metadata.get(key);\n                    },\n                    enumerable: true\n                });\n            }\n        });\n        registry.set(this.issuer, this);\n        const Client = getClient(this, aadIssValidation);\n        Object.defineProperties(this, {\n            Client: {\n                value: Client,\n                enumerable: true\n            },\n            FAPI1Client: {\n                value: class FAPI1Client extends Client {\n                },\n                enumerable: true\n            },\n            FAPI2Client: {\n                value: class FAPI2Client extends Client {\n                },\n                enumerable: true\n            }\n        });\n    }\n    get metadata() {\n        return clone(Object.fromEntries(this.#metadata.entries()));\n    }\n    static async webfinger(input) {\n        const resource = webfingerNormalize(input);\n        const { host } = url.parse(resource);\n        const webfingerUrl = `https://${host}/.well-known/webfinger`;\n        const response = await request.call(this, {\n            method: \"GET\",\n            url: webfingerUrl,\n            responseType: \"json\",\n            searchParams: {\n                resource,\n                rel: \"http://openid.net/specs/connect/1.0/issuer\"\n            },\n            headers: {\n                Accept: \"application/json\"\n            }\n        });\n        const body = processResponse(response);\n        const location = Array.isArray(body.links) && body.links.find((link)=>typeof link === \"object\" && link.rel === \"http://openid.net/specs/connect/1.0/issuer\" && link.href);\n        if (!location) {\n            throw new RPError({\n                message: \"no issuer found in webfinger response\",\n                body\n            });\n        }\n        if (typeof location.href !== \"string\" || !location.href.startsWith(\"https://\")) {\n            throw new RPError({\n                printf: [\n                    \"invalid issuer location %s\",\n                    location.href\n                ],\n                body\n            });\n        }\n        const expectedIssuer = location.href;\n        if (registry.has(expectedIssuer)) {\n            return registry.get(expectedIssuer);\n        }\n        const issuer = await this.discover(expectedIssuer);\n        if (issuer.issuer !== expectedIssuer) {\n            registry.del(issuer.issuer);\n            throw new RPError(\"discovered issuer mismatch, expected %s, got: %s\", expectedIssuer, issuer.issuer);\n        }\n        return issuer;\n    }\n    static async discover(uri) {\n        const wellKnownUri = resolveWellKnownUri(uri);\n        const response = await request.call(this, {\n            method: \"GET\",\n            responseType: \"json\",\n            url: wellKnownUri,\n            headers: {\n                Accept: \"application/json\"\n            }\n        });\n        const body = processResponse(response);\n        return new Issuer({\n            ...ISSUER_DEFAULTS,\n            ...body,\n            [AAD_MULTITENANT]: !!AAD_MULTITENANT_DISCOVERY.find((discoveryURL)=>wellKnownUri.startsWith(discoveryURL))\n        });\n    }\n    async reloadJwksUri() {\n        await keystore.call(this, true);\n    }\n    /* istanbul ignore next */ [inspect.custom]() {\n        return `${this.constructor.name} ${inspect(this.metadata, {\n            depth: Infinity,\n            colors: process.stdout.isTTY,\n            compact: false,\n            sorted: true\n        })}`;\n    }\n}\nfunction resolveWellKnownUri(uri) {\n    const parsed = url.parse(uri);\n    if (parsed.pathname.includes(\"/.well-known/\")) {\n        return uri;\n    } else {\n        let pathname;\n        if (parsed.pathname.endsWith(\"/\")) {\n            pathname = `${parsed.pathname}.well-known/openid-configuration`;\n        } else {\n            pathname = `${parsed.pathname}/.well-known/openid-configuration`;\n        }\n        return url.format({\n            ...parsed,\n            pathname\n        });\n    }\n}\nmodule.exports = Issuer;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/issuer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/issuer_registry.js":
/*!***********************************************************!*\
  !*** ./node_modules/openid-client/lib/issuer_registry.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const LRU = __webpack_require__(/*! lru-cache */ \"(rsc)/./node_modules/lru-cache/index.js\");\nmodule.exports = new LRU({\n    max: 100\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaXNzdWVyX3JlZ2lzdHJ5LmpzIiwibWFwcGluZ3MiOiJBQUFBLE1BQU1BLE1BQU1DLG1CQUFPQSxDQUFDO0FBRXBCQyxPQUFPQyxPQUFPLEdBQUcsSUFBSUgsSUFBSTtJQUFFSSxLQUFLO0FBQUkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaHJvbm9zLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL2lzc3Vlcl9yZWdpc3RyeS5qcz9iMDZkIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IExSVSA9IHJlcXVpcmUoJ2xydS1jYWNoZScpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IG5ldyBMUlUoeyBtYXg6IDEwMCB9KTtcbiJdLCJuYW1lcyI6WyJMUlUiLCJyZXF1aXJlIiwibW9kdWxlIiwiZXhwb3J0cyIsIm1heCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/issuer_registry.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/passport_strategy.js":
/*!*************************************************************!*\
  !*** ./node_modules/openid-client/lib/passport_strategy.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const url = __webpack_require__(/*! url */ \"url\");\nconst { format } = __webpack_require__(/*! util */ \"util\");\nconst cloneDeep = __webpack_require__(/*! ./helpers/deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst { RPError, OPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst { BaseClient } = __webpack_require__(/*! ./client */ \"(rsc)/./node_modules/openid-client/lib/client.js\");\nconst { random, codeChallenge } = __webpack_require__(/*! ./helpers/generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst pick = __webpack_require__(/*! ./helpers/pick */ \"(rsc)/./node_modules/openid-client/lib/helpers/pick.js\");\nconst { resolveResponseType, resolveRedirectUri } = __webpack_require__(/*! ./helpers/client */ \"(rsc)/./node_modules/openid-client/lib/helpers/client.js\");\nfunction verified(err, user, info = {}) {\n    if (err) {\n        this.error(err);\n    } else if (!user) {\n        this.fail(info);\n    } else {\n        this.success(user, info);\n    }\n}\nfunction OpenIDConnectStrategy({ client, params = {}, passReqToCallback = false, sessionKey, usePKCE = true, extras = {} } = {}, verify) {\n    if (!(client instanceof BaseClient)) {\n        throw new TypeError(\"client must be an instance of openid-client Client\");\n    }\n    if (typeof verify !== \"function\") {\n        throw new TypeError(\"verify callback must be a function\");\n    }\n    if (!client.issuer || !client.issuer.issuer) {\n        throw new TypeError(\"client must have an issuer with an identifier\");\n    }\n    this._client = client;\n    this._issuer = client.issuer;\n    this._verify = verify;\n    this._passReqToCallback = passReqToCallback;\n    this._usePKCE = usePKCE;\n    this._key = sessionKey || `oidc:${url.parse(this._issuer.issuer).hostname}`;\n    this._params = cloneDeep(params);\n    // state and nonce are handled in authenticate()\n    delete this._params.state;\n    delete this._params.nonce;\n    this._extras = cloneDeep(extras);\n    if (!this._params.response_type) this._params.response_type = resolveResponseType.call(client);\n    if (!this._params.redirect_uri) this._params.redirect_uri = resolveRedirectUri.call(client);\n    if (!this._params.scope) this._params.scope = \"openid\";\n    if (this._usePKCE === true) {\n        const supportedMethods = Array.isArray(this._issuer.code_challenge_methods_supported) ? this._issuer.code_challenge_methods_supported : false;\n        if (supportedMethods && supportedMethods.includes(\"S256\")) {\n            this._usePKCE = \"S256\";\n        } else if (supportedMethods && supportedMethods.includes(\"plain\")) {\n            this._usePKCE = \"plain\";\n        } else if (supportedMethods) {\n            throw new TypeError(\"neither code_challenge_method supported by the client is supported by the issuer\");\n        } else {\n            this._usePKCE = \"S256\";\n        }\n    } else if (typeof this._usePKCE === \"string\" && ![\n        \"plain\",\n        \"S256\"\n    ].includes(this._usePKCE)) {\n        throw new TypeError(`${this._usePKCE} is not valid/implemented PKCE code_challenge_method`);\n    }\n    this.name = url.parse(client.issuer.issuer).hostname;\n}\nOpenIDConnectStrategy.prototype.authenticate = function authenticate(req, options) {\n    (async ()=>{\n        const client = this._client;\n        if (!req.session) {\n            throw new TypeError(\"authentication requires session support\");\n        }\n        const reqParams = client.callbackParams(req);\n        const sessionKey = this._key;\n        const { 0: parameter, length } = Object.keys(reqParams);\n        /**\n     * Start authentication request if this has no authorization response parameters or\n     * this might a login initiated from a third party as per\n     * https://openid.net/specs/openid-connect-core-1_0.html#ThirdPartyInitiatedLogin.\n     */ if (length === 0 || length === 1 && parameter === \"iss\") {\n            // provide options object with extra authentication parameters\n            const params = {\n                state: random(),\n                ...this._params,\n                ...options\n            };\n            if (!params.nonce && params.response_type.includes(\"id_token\")) {\n                params.nonce = random();\n            }\n            req.session[sessionKey] = pick(params, \"nonce\", \"state\", \"max_age\", \"response_type\");\n            if (this._usePKCE && params.response_type.includes(\"code\")) {\n                const verifier = random();\n                req.session[sessionKey].code_verifier = verifier;\n                switch(this._usePKCE){\n                    case \"S256\":\n                        params.code_challenge = codeChallenge(verifier);\n                        params.code_challenge_method = \"S256\";\n                        break;\n                    case \"plain\":\n                        params.code_challenge = verifier;\n                        break;\n                }\n            }\n            this.redirect(client.authorizationUrl(params));\n            return;\n        }\n        /* end authentication request */ /* start authentication response */ const session = req.session[sessionKey];\n        if (Object.keys(session || {}).length === 0) {\n            throw new Error(format('did not find expected authorization request details in session, req.session[\"%s\"] is %j', sessionKey, session));\n        }\n        const { state, nonce, max_age: maxAge, code_verifier: codeVerifier, response_type: responseType } = session;\n        try {\n            delete req.session[sessionKey];\n        } catch (err) {}\n        const opts = {\n            redirect_uri: this._params.redirect_uri,\n            ...options\n        };\n        const checks = {\n            state,\n            nonce,\n            max_age: maxAge,\n            code_verifier: codeVerifier,\n            response_type: responseType\n        };\n        const tokenset = await client.callback(opts.redirect_uri, reqParams, checks, this._extras);\n        const passReq = this._passReqToCallback;\n        const loadUserinfo = this._verify.length > (passReq ? 3 : 2) && client.issuer.userinfo_endpoint;\n        const args = [\n            tokenset,\n            verified.bind(this)\n        ];\n        if (loadUserinfo) {\n            if (!tokenset.access_token) {\n                throw new RPError({\n                    message: \"expected access_token to be returned when asking for userinfo in verify callback\",\n                    tokenset\n                });\n            }\n            const userinfo = await client.userinfo(tokenset);\n            args.splice(1, 0, userinfo);\n        }\n        if (passReq) {\n            args.unshift(req);\n        }\n        this._verify(...args);\n    /* end authentication response */ })().catch((error)=>{\n        if (error instanceof OPError && error.error !== \"server_error\" && !error.error.startsWith(\"invalid\") || error instanceof RPError) {\n            this.fail(error);\n        } else {\n            this.error(error);\n        }\n    });\n};\nmodule.exports = OpenIDConnectStrategy;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/passport_strategy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/token_set.js":
/*!*****************************************************!*\
  !*** ./node_modules/openid-client/lib/token_set.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const base64url = __webpack_require__(/*! ./helpers/base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\nconst now = __webpack_require__(/*! ./helpers/unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\nclass TokenSet {\n    constructor(values){\n        Object.assign(this, values);\n        const { constructor, ...properties } = Object.getOwnPropertyDescriptors(this.constructor.prototype);\n        Object.defineProperties(this, properties);\n    }\n    set expires_in(value) {\n        this.expires_at = now() + Number(value);\n    }\n    get expires_in() {\n        return Math.max.apply(null, [\n            this.expires_at - now(),\n            0\n        ]);\n    }\n    expired() {\n        return this.expires_in === 0;\n    }\n    claims() {\n        if (!this.id_token) {\n            throw new TypeError(\"id_token not present in TokenSet\");\n        }\n        return JSON.parse(base64url.decode(this.id_token.split(\".\")[1]));\n    }\n}\nmodule.exports = TokenSet;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvdG9rZW5fc2V0LmpzIiwibWFwcGluZ3MiOiJBQUFBLE1BQU1BLFlBQVlDLG1CQUFPQSxDQUFDO0FBQzFCLE1BQU1DLE1BQU1ELG1CQUFPQSxDQUFDO0FBRXBCLE1BQU1FO0lBQ0pDLFlBQVlDLE1BQU0sQ0FBRTtRQUNsQkMsT0FBT0MsTUFBTSxDQUFDLElBQUksRUFBRUY7UUFDcEIsTUFBTSxFQUFFRCxXQUFXLEVBQUUsR0FBR0ksWUFBWSxHQUFHRixPQUFPRyx5QkFBeUIsQ0FDckUsSUFBSSxDQUFDTCxXQUFXLENBQUNNLFNBQVM7UUFHNUJKLE9BQU9LLGdCQUFnQixDQUFDLElBQUksRUFBRUg7SUFDaEM7SUFFQSxJQUFJSSxXQUFXQyxLQUFLLEVBQUU7UUFDcEIsSUFBSSxDQUFDQyxVQUFVLEdBQUdaLFFBQVFhLE9BQU9GO0lBQ25DO0lBRUEsSUFBSUQsYUFBYTtRQUNmLE9BQU9JLEtBQUtDLEdBQUcsQ0FBQ0MsS0FBSyxDQUFDLE1BQU07WUFBQyxJQUFJLENBQUNKLFVBQVUsR0FBR1o7WUFBTztTQUFFO0lBQzFEO0lBRUFpQixVQUFVO1FBQ1IsT0FBTyxJQUFJLENBQUNQLFVBQVUsS0FBSztJQUM3QjtJQUVBUSxTQUFTO1FBQ1AsSUFBSSxDQUFDLElBQUksQ0FBQ0MsUUFBUSxFQUFFO1lBQ2xCLE1BQU0sSUFBSUMsVUFBVTtRQUN0QjtRQUVBLE9BQU9DLEtBQUtDLEtBQUssQ0FBQ3hCLFVBQVV5QixNQUFNLENBQUMsSUFBSSxDQUFDSixRQUFRLENBQUNLLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtJQUNoRTtBQUNGO0FBRUFDLE9BQU9DLE9BQU8sR0FBR3pCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2hyb25vcy1wbGF0Zm9ybS8uL25vZGVfbW9kdWxlcy9vcGVuaWQtY2xpZW50L2xpYi90b2tlbl9zZXQuanM/MjgzYiJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBiYXNlNjR1cmwgPSByZXF1aXJlKCcuL2hlbHBlcnMvYmFzZTY0dXJsJyk7XG5jb25zdCBub3cgPSByZXF1aXJlKCcuL2hlbHBlcnMvdW5peF90aW1lc3RhbXAnKTtcblxuY2xhc3MgVG9rZW5TZXQge1xuICBjb25zdHJ1Y3Rvcih2YWx1ZXMpIHtcbiAgICBPYmplY3QuYXNzaWduKHRoaXMsIHZhbHVlcyk7XG4gICAgY29uc3QgeyBjb25zdHJ1Y3RvciwgLi4ucHJvcGVydGllcyB9ID0gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcnMoXG4gICAgICB0aGlzLmNvbnN0cnVjdG9yLnByb3RvdHlwZSxcbiAgICApO1xuXG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnRpZXModGhpcywgcHJvcGVydGllcyk7XG4gIH1cblxuICBzZXQgZXhwaXJlc19pbih2YWx1ZSkge1xuICAgIHRoaXMuZXhwaXJlc19hdCA9IG5vdygpICsgTnVtYmVyKHZhbHVlKTtcbiAgfVxuXG4gIGdldCBleHBpcmVzX2luKCkge1xuICAgIHJldHVybiBNYXRoLm1heC5hcHBseShudWxsLCBbdGhpcy5leHBpcmVzX2F0IC0gbm93KCksIDBdKTtcbiAgfVxuXG4gIGV4cGlyZWQoKSB7XG4gICAgcmV0dXJuIHRoaXMuZXhwaXJlc19pbiA9PT0gMDtcbiAgfVxuXG4gIGNsYWltcygpIHtcbiAgICBpZiAoIXRoaXMuaWRfdG9rZW4pIHtcbiAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ2lkX3Rva2VuIG5vdCBwcmVzZW50IGluIFRva2VuU2V0Jyk7XG4gICAgfVxuXG4gICAgcmV0dXJuIEpTT04ucGFyc2UoYmFzZTY0dXJsLmRlY29kZSh0aGlzLmlkX3Rva2VuLnNwbGl0KCcuJylbMV0pKTtcbiAgfVxufVxuXG5tb2R1bGUuZXhwb3J0cyA9IFRva2VuU2V0O1xuIl0sIm5hbWVzIjpbImJhc2U2NHVybCIsInJlcXVpcmUiLCJub3ciLCJUb2tlblNldCIsImNvbnN0cnVjdG9yIiwidmFsdWVzIiwiT2JqZWN0IiwiYXNzaWduIiwicHJvcGVydGllcyIsImdldE93blByb3BlcnR5RGVzY3JpcHRvcnMiLCJwcm90b3R5cGUiLCJkZWZpbmVQcm9wZXJ0aWVzIiwiZXhwaXJlc19pbiIsInZhbHVlIiwiZXhwaXJlc19hdCIsIk51bWJlciIsIk1hdGgiLCJtYXgiLCJhcHBseSIsImV4cGlyZWQiLCJjbGFpbXMiLCJpZF90b2tlbiIsIlR5cGVFcnJvciIsIkpTT04iLCJwYXJzZSIsImRlY29kZSIsInNwbGl0IiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/token_set.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/package.json":
/*!*************************************************!*\
  !*** ./node_modules/openid-client/package.json ***!
  \*************************************************/
/***/ ((module) => {

"use strict";
module.exports = JSON.parse('{"name":"openid-client","version":"5.7.1","description":"OpenID Connect Relying Party (RP, Client) implementation for Node.js runtime, supports passportjs","keywords":["auth","authentication","basic","certified","client","connect","dynamic","electron","hybrid","identity","implicit","oauth","oauth2","oidc","openid","passport","relying party","strategy"],"homepage":"https://github.com/panva/openid-client","repository":"panva/openid-client","funding":{"url":"https://github.com/sponsors/panva"},"license":"MIT","author":"Filip Skokan <<EMAIL>>","exports":{"types":"./types/index.d.ts","import":"./lib/index.mjs","require":"./lib/index.js"},"main":"./lib/index.js","types":"./types/index.d.ts","files":["lib","types/index.d.ts"],"scripts":{"format":"npx prettier --loglevel silent --write ./lib ./test ./certification ./types","test":"mocha test/**/*.test.js"},"dependencies":{"jose":"^4.15.9","lru-cache":"^6.0.0","object-hash":"^2.2.0","oidc-token-hash":"^5.0.3"},"devDependencies":{"@types/node":"^16.18.106","@types/passport":"^1.0.16","base64url":"^3.0.1","chai":"^4.5.0","mocha":"^10.7.3","nock":"^13.5.5","prettier":"^2.8.8","readable-mock-req":"^0.2.2","sinon":"^9.2.4","timekeeper":"^2.3.1"},"standard-version":{"scripts":{"postchangelog":"sed -i \'\' -e \'s/### \\\\[/## [/g\' CHANGELOG.md"},"types":[{"type":"feat","section":"Features"},{"type":"fix","section":"Fixes"},{"type":"chore","hidden":true},{"type":"docs","hidden":true},{"type":"style","hidden":true},{"type":"refactor","section":"Refactor","hidden":false},{"type":"perf","section":"Performance","hidden":false},{"type":"test","hidden":true}]}}');

/***/ })

};
;