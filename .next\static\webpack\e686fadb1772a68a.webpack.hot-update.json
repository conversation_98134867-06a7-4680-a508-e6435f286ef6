{"c": ["app/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDouglas%5CDesktop%5CChronos%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CDouglas%5CDesktop%5CChronos%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Raleway%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-raleway%22%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%5D%7D%5D%2C%22variableName%22%3A%22raleway%22%7D&modules=C%3A%5CUsers%5CDouglas%5CDesktop%5CChronos%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Cinzel%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-cinzel%22%2C%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%5D%7D%5D%2C%22variableName%22%3A%22cinzel%22%7D&modules=C%3A%5CUsers%5CDouglas%5CDesktop%5CChronos%5Csrc%5Ccomponents%5Cproviders.tsx&modules=C%3A%5CUsers%5CDouglas%5CDesktop%5CChronos%5Csrc%5Ccomponents%5Cui%5Ctoaster.tsx&server=false!"]}