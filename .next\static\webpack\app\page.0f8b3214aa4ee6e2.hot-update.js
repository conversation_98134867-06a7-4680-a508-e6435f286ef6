"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/hero-section.tsx":
/*!**************************************************!*\
  !*** ./src/components/sections/hero-section.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeroSection: function() { return /* binding */ HeroSection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Shield_Sparkles_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Shield,Sparkles,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Shield_Sparkles_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Shield,Sparkles,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Shield_Sparkles_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Shield,Sparkles,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Shield_Sparkles_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Shield,Sparkles,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Shield_Sparkles_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Shield,Sparkles,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Shield_Sparkles_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Shield,Sparkles,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Shield_Sparkles_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Shield,Sparkles,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* __next_internal_client_entry_do_not_use__ HeroSection auto */ \n\n\n\n\nconst metrics = [\n    {\n        icon: _barrel_optimize_names_Clock_DollarSign_Shield_Sparkles_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        label: \"Jackpot\",\n        value: \"11.000\",\n        suffix: \"Chronos\",\n        color: \"text-chronos-gold\"\n    },\n    {\n        icon: _barrel_optimize_names_Clock_DollarSign_Shield_Sparkles_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        label: \"Di\\xe1ria\",\n        value: \"500\",\n        suffix: \"Chronos\",\n        color: \"text-green-400\"\n    },\n    {\n        icon: _barrel_optimize_names_Clock_DollarSign_Shield_Sparkles_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        label: \"Jogadores\",\n        value: \"2.847\",\n        suffix: \"Online\",\n        color: \"text-blue-400\"\n    },\n    {\n        icon: _barrel_optimize_names_Clock_DollarSign_Shield_Sparkles_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        label: \"Pr\\xf3ximo\",\n        value: \"05:23\",\n        suffix: \"Minutos\",\n        color: \"text-orange-400\"\n    }\n];\nconst features = [\n    {\n        icon: _barrel_optimize_names_Clock_DollarSign_Shield_Sparkles_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"Transpar\\xeancia Total\",\n        description: \"Sorteios com prova criptogr\\xe1fica e auditoria p\\xfablica\"\n    },\n    {\n        icon: _barrel_optimize_names_Clock_DollarSign_Shield_Sparkles_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        title: \"Tempo Real\",\n        description: \"Resultados instant\\xe2neos e chat ao vivo\"\n    },\n    {\n        icon: _barrel_optimize_names_Clock_DollarSign_Shield_Sparkles_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        title: \"M\\xfaltiplos Formatos\",\n        description: \"Demo, Individual, X1 e Batalhas em Grupo\"\n    }\n];\nfunction HeroSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"hero\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hero-background\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hero-statue left-statue\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chronos\\\\src\\\\components\\\\sections\\\\hero-section.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hero-statue right-statue\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chronos\\\\src\\\\components\\\\sections\\\\hero-section.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"particles-container\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chronos\\\\src\\\\components\\\\sections\\\\hero-section.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chronos\\\\src\\\\components\\\\sections\\\\hero-section.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hero-container\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hero-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            className: \"hero-title\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"hero-title-main\",\n                                    children: \"CHRONOS\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chronos\\\\src\\\\components\\\\sections\\\\hero-section.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"hero-title-sub\",\n                                    children: \"PLATFORM\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chronos\\\\src\\\\components\\\\sections\\\\hero-section.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chronos\\\\src\\\\components\\\\sections\\\\hero-section.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.p, {\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.8\n                            },\n                            className: \"hero-description\",\n                            children: \"A plataforma de sorteios mais transparente e segura do Brasil. Participe de sorteios exclusivos com tecnologia blockchain e ganhe pr\\xeamios incr\\xedveis.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chronos\\\\src\\\\components\\\\sections\\\\hero-section.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hero-stats\",\n                            children: metrics.map((metric, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    className: \"stat-card\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.4 + index * 0.1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"stat-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(metric.icon, {\n                                                className: \"\".concat(metric.color)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chronos\\\\src\\\\components\\\\sections\\\\hero-section.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chronos\\\\src\\\\components\\\\sections\\\\hero-section.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"stat-value\",\n                                            children: metric.value\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chronos\\\\src\\\\components\\\\sections\\\\hero-section.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"stat-label\",\n                                            children: metric.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chronos\\\\src\\\\components\\\\sections\\\\hero-section.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-chronos-gold font-medium mt-1\",\n                                            children: metric.suffix\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chronos\\\\src\\\\components\\\\sections\\\\hero-section.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chronos\\\\src\\\\components\\\\sections\\\\hero-section.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chronos\\\\src\\\\components\\\\sections\\\\hero-section.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 1.2\n                            },\n                            className: \"hero-cta\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    asChild: true,\n                                    className: \"btn-hero-primary\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/auth/signup\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Shield_Sparkles_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chronos\\\\src\\\\components\\\\sections\\\\hero-section.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Come\\xe7ar Agora\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chronos\\\\src\\\\components\\\\sections\\\\hero-section.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chronos\\\\src\\\\components\\\\sections\\\\hero-section.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    asChild: true,\n                                    variant: \"outline\",\n                                    className: \"btn-hero-secondary\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/demo\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Shield_Sparkles_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chronos\\\\src\\\\components\\\\sections\\\\hero-section.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Experimentar Demo\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chronos\\\\src\\\\components\\\\sections\\\\hero-section.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chronos\\\\src\\\\components\\\\sections\\\\hero-section.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chronos\\\\src\\\\components\\\\sections\\\\hero-section.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chronos\\\\src\\\\components\\\\sections\\\\hero-section.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chronos\\\\src\\\\components\\\\sections\\\\hero-section.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hero-scroll-indicator\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                    className: \"fas fa-chevron-down\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chronos\\\\src\\\\components\\\\sections\\\\hero-section.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chronos\\\\src\\\\components\\\\sections\\\\hero-section.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chronos\\\\src\\\\components\\\\sections\\\\hero-section.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n_c = HeroSection;\nvar _c;\n$RefreshReg$(_c, \"HeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/hero-section.tsx\n"));

/***/ })

});