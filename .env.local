# Database
DATABASE_URL="postgresql://postgres:password@localhost:5432/chronos_db"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="chronos-secret-key-development-only"

# Stripe (Test Keys)
STRIPE_PUBLISHABLE_KEY="pk_test_development"
STRIPE_SECRET_KEY="sk_test_development"
STRIPE_WEBHOOK_SECRET="whsec_development"

# PayPal (Sandbox)
PAYPAL_CLIENT_ID="paypal-client-id-sandbox"
PAYPAL_CLIENT_SECRET="paypal-client-secret-sandbox"
PAYPAL_ENVIRONMENT="sandbox"

# Pix (Gerencianet Sandbox)
GERENCIANET_CLIENT_ID="gerencianet-client-id-sandbox"
GERENCIANET_CLIENT_SECRET="gerencianet-client-secret-sandbox"
GERENCIANET_ENVIRONMENT="sandbox"

# Redis (Local)
REDIS_URL="redis://localhost:6379"

# Email (Development)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="development-password"

# AWS S3 (Development)
AWS_ACCESS_KEY_ID="development-access-key"
AWS_SECRET_ACCESS_KEY="development-secret-key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="chronos-platform-dev"

# Socket.IO
SOCKET_IO_SECRET="socket-secret-development"
NEXT_PUBLIC_SOCKET_URL="http://localhost:3001"

# Crypto Seeds
CRYPTO_SEED_SECRET="crypto-seed-secret-development"

# App Settings
APP_NAME="Chronos Platform"
APP_URL="http://localhost:3000"
SUPPORT_EMAIL="<EMAIL>"

# Development flags
NODE_ENV="development"
NEXT_PUBLIC_APP_ENV="development"
