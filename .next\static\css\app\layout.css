/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./src/app/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700;800&family=Raleway:wght@300;400;500;600;700;800&display=swap');

*, ::before, ::after{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: Raleway, Inter, system-ui, sans-serif; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

*{
  border-color: hsl(var(--border));
}

body{
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
    font-feature-settings: "rlig" 1, "calt" 1;
}
.container{
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 2rem;
  padding-left: 2rem;
}
@media (min-width: 1400px){

  .container{
    max-width: 1400px;
  }
}
/* Chronos specific styles */
.chronos-card {
    background: var(--gradient-card);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-card);
    -webkit-backdrop-filter: blur(5px);
            backdrop-filter: blur(5px);
    transition: all 0.4s ease;
  }
.chronos-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
    border-color: rgba(212, 175, 55, 0.3);
  }
.chronos-button-primary {
    background: var(--gradient-maroon);
    color: var(--marmore-ivory);
    border: none;
    padding: 12px 24px;
    border-radius: var(--radius-md);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-button);
    font-family: var(--font-primary);
  }
.chronos-button-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(123, 24, 24, 0.4);
  }
.chronos-button-gold {
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    border: none;
    padding: 12px 24px;
    border-radius: var(--radius-md);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-button);
    font-family: var(--font-primary);
  }
.chronos-button-gold:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
  }
.chronos-text-gold {
    color: var(--ouro-antigo);
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
.chronos-text-gradient {
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-family: var(--font-titles);
    font-weight: 700;
    letter-spacing: 2px;
  }
/* Header Styles */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(245, 243, 238, 0.95);
    -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
    z-index: var(--z-header);
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
.header.scrolled {
    background: rgba(47, 47, 47, 0.95);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
  }
.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
  }
.nav-logo {
    display: flex;
    align-items: center;
    gap: 12px;
  }
.logo {
    height: 40px;
    width: auto;
    transition: var(--transition-normal);
  }
.logo:hover {
    transform: rotate(10deg);
  }
.logo-text {
    font-family: var(--font-titles);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--chronos-maroon);
    letter-spacing: 3px;
    transition: var(--transition-normal);
    text-transform: uppercase;
    text-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }
.nav-menu {
    display: flex;
    align-items: center;
    gap: 2rem;
  }
.nav-link {
    text-decoration: none;
    color: var(--carvao-noturno);
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
  }
.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -4px;
    left: 0;
    background: var(--chronos-maroon);
    transition: width 0.3s ease;
  }
.nav-link:hover::after,
  .nav-link:focus::after {
    width: 100%;
  }
.nav-link:hover {
    color: var(--chronos-maroon);
  }
.btn-login {
    background: transparent;
    border: 2px solid var(--chronos-maroon);
    color: var(--chronos-maroon);
    padding: 8px 20px;
    border-radius: var(--radius-md);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
  }
.btn-login:hover {
    background: var(--chronos-maroon);
    color: white;
    transform: translateY(-2px);
  }
.btn-primary {
    background: var(--gradient-gold);
    border: none;
    color: var(--carvao-noturno);
    padding: 10px 24px;
    border-radius: var(--radius-md);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-button);
  }
.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
  }
/* Hero Section */
.hero {
    min-height: 100vh;
    background: var(--gradient-hero);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    padding-top: 80px;
  }
.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.1;
    z-index: var(--z-back);
  }
.hero-statue {
    position: absolute;
    width: 300px;
    height: 400px;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M50 10 L60 30 L50 50 L40 30 Z" fill="%23D4AF37" opacity="0.3"/></svg>') no-repeat center;
    background-size: contain;
    transition: all 5s ease-in-out;
  }
.left-statue {
    left: -100px;
    top: 20%;
    transform: rotate(-10deg);
    animation: floatLeft 8s ease-in-out infinite alternate;
  }
.right-statue {
    right: -100px;
    top: 30%;
    transform: rotate(10deg);
    animation: floatRight 10s ease-in-out infinite alternate;
  }
.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    text-align: center;
    z-index: var(--z-normal);
    position: relative;
  }
.hero-content {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 1s ease, transform 1s ease;
  }
.hero-title {
    margin-bottom: 2rem;
  }
.hero-title-main {
    display: block;
    font-family: var(--font-titles);
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: 700;
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: 8px;
    text-shadow: 0 5px 20px rgba(0, 0, 0, 0.3), 0 2px 5px rgba(212, 175, 55, 0.2);
    opacity: 1;
    text-transform: uppercase;
    animation: fadeInUp 1s ease 0.2s forwards;
  }
.hero-title-sub {
    display: block;
    font-family: var(--font-titles);
    font-size: clamp(1.5rem, 4vw, 2.5rem);
    font-weight: 500;
    color: var(--marmore-ivory);
    letter-spacing: 10px;
    margin-top: 0.8rem;
    animation: fadeInUp 1s ease 0.5s forwards;
    opacity: 1;
    text-transform: uppercase;
  }
.hero-description {
    max-width: 700px;
    margin: 0 auto 3rem;
    font-size: clamp(1rem, 2vw, 1.25rem);
    color: rgba(245, 243, 238, 0.9);
    animation: fadeIn 1s ease 0.8s forwards;
    opacity: 1;
  }
.hero-stats {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
  }
.hero-cta {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-top: 2rem;
    animation: fadeIn 1s ease 1.2s forwards;
    opacity: 1;
  }
.btn-hero-primary {
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    border: none;
    padding: 15px 30px;
    border-radius: var(--radius-md);
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-button);
  }
.btn-hero-primary:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(212, 175, 55, 0.5);
  }
.btn-hero-secondary {
    background: rgba(255, 255, 255, 0.1);
    -webkit-backdrop-filter: blur(5px);
            backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--marmore-ivory);
    padding: 15px 30px;
    border-radius: var(--radius-md);
    font-size: 1.1rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
  }
.btn-hero-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  }
.hero-scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 1.5rem;
    color: var(--marmore-ivory);
    cursor: pointer;
    animation: bounce 2s infinite;
    opacity: 0.7;
    transition: opacity 0.3s ease;
  }
.hero-scroll-indicator:hover {
    opacity: 1;
  }
/* Stat Cards */
.stat-card {
    background: var(--gradient-card);
    border-radius: var(--radius-lg);
    padding: 20px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
    transition: all 0.4s ease;
    box-shadow: var(--shadow-card);
  }
.stat-card:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.4);
    border-color: rgba(212, 175, 55, 0.3);
  }
.stat-icon {
    font-size: 2rem;
    margin-bottom: 10px;
  }
.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--ouro-antigo);
    font-family: var(--font-titles);
    margin-bottom: 5px;
  }
.stat-label {
    font-size: 0.9rem;
    color: rgba(245, 243, 238, 0.7);
    text-transform: uppercase;
    letter-spacing: 1px;
  }
/* Current Sweepstakes Section */
.current-sweepstakes {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--carvao-noturno) 0%, #1a1a1a 100%);
    position: relative;
  }
.section-header {
    text-align: center;
    margin-bottom: 4rem;
  }
.section-title {
    font-family: var(--font-titles);
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
    letter-spacing: 2px;
  }
.section-subtitle {
    font-size: 1.2rem;
    color: rgba(245, 243, 238, 0.8);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }
.sweepstakes-card {
    max-width: 800px;
    margin: 0 auto;
    background: var(--gradient-card);
    border-radius: var(--radius-xl);
    padding: 3rem;
    box-shadow: var(--shadow-hero);
    border: 1px solid rgba(255, 255, 255, 0.1);
    -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
    transition: all 0.4s ease;
  }
.sweepstakes-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 30px 80px rgba(0, 0, 0, 0.6);
  }
.sweepstakes-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
  }
.sweepstakes-header h2 {
    font-family: var(--font-titles);
    font-size: 2rem;
    color: var(--marmore-ivory);
    margin: 0;
    letter-spacing: 1px;
  }
.sweepstakes-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
  }
.sweepstakes-stat {
    text-align: center;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
  }
.sweepstakes-stat:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-5px);
  }
.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--ouro-antigo);
    font-family: var(--font-titles);
    margin-bottom: 0.5rem;
  }
.stat-text {
    font-size: 0.9rem;
    color: rgba(245, 243, 238, 0.7);
    text-transform: uppercase;
    letter-spacing: 1px;
  }
.sweepstakes-timer {
    text-align: center;
    margin-bottom: 2rem;
  }
.timer-label {
    display: block;
    font-size: 1rem;
    color: rgba(245, 243, 238, 0.8);
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
  }
.timer {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
  }
.timer-unit {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    padding: 1rem;
    min-width: 80px;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
.timer-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--ouro-antigo);
    font-family: var(--font-titles);
  }
.timer-text {
    display: block;
    font-size: 0.8rem;
    color: rgba(245, 243, 238, 0.7);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-top: 0.5rem;
  }
.btn-sweepstakes {
    width: 100%;
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--radius-md);
    font-size: 1.2rem;
    font-weight: 700;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-button);
    margin-bottom: 2rem;
    text-transform: uppercase;
    letter-spacing: 1px;
  }
.btn-sweepstakes:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(212, 175, 55, 0.5);
  }
.sweepstakes-participants {
    text-align: center;
  }
.participants-avatars {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }
.participant-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
    border: 2px solid rgba(255, 255, 255, 0.2);
  }
.participant-count {
    font-size: 0.9rem;
    color: var(--ouro-antigo);
    font-weight: 600;
    margin-left: 0.5rem;
  }
.participants-message {
    font-size: 0.9rem;
    color: rgba(245, 243, 238, 0.7);
    margin: 0;
  }
/* Authentication Pages */
.auth-page {
    min-height: 100vh;
    background: var(--gradient-hero);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    padding: 2rem 1rem;
  }
.auth-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.1;
    z-index: var(--z-back);
  }
.auth-container {
    max-width: 500px;
    width: 100%;
    z-index: var(--z-normal);
    position: relative;
  }
.auth-back {
    margin-bottom: 2rem;
  }
.auth-back-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(245, 243, 238, 0.7);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
  }
.auth-back-link:hover {
    color: var(--ouro-antigo);
  }
.auth-logo {
    text-align: center;
    margin-bottom: 2rem;
  }
.auth-logo-text {
    font-family: var(--font-titles);
    font-size: 2rem;
    font-weight: 700;
    color: var(--ouro-antigo);
    letter-spacing: 3px;
    margin-top: 0.5rem;
    text-transform: uppercase;
  }
.auth-progress {
    margin-bottom: 2rem;
  }
.auth-progress-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 1rem;
  }
.auth-progress-fill {
    height: 100%;
    background: var(--gradient-gold);
    transition: width 0.3s ease;
  }
.auth-progress-steps {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: rgba(245, 243, 238, 0.6);
  }
.auth-progress-steps span.active {
    color: var(--ouro-antigo);
    font-weight: 600;
  }
.auth-card-container {
    margin-bottom: 2rem;
  }
.auth-card {
    background: var(--gradient-card);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-hero);
    -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
  }
.auth-card-header {
    text-align: center;
    padding: 2rem 2rem 1rem;
  }
.auth-card-title {
    font-family: var(--font-titles);
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--marmore-ivory);
    margin-bottom: 0.5rem;
  }
.auth-card-description {
    color: rgba(245, 243, 238, 0.7);
    font-size: 1rem;
    line-height: 1.5;
  }
.auth-card-content {
    padding: 1rem 2rem 2rem;
  }
.auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }
.auth-field {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
.auth-label {
    font-weight: 600;
    color: var(--marmore-ivory);
    font-size: 0.9rem;
  }
.auth-input-container {
    position: relative;
    display: flex;
    align-items: center;
  }
.auth-input-icon {
    position: absolute;
    left: 12px;
    width: 18px;
    height: 18px;
    color: rgba(245, 243, 238, 0.5);
    z-index: 1;
  }
.auth-input {
    width: 100%;
    padding: 12px 16px 12px 44px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    color: var(--marmore-ivory);
    font-size: 1rem;
    transition: all 0.3s ease;
  }
.auth-input:focus {
    outline: none;
    border-color: var(--ouro-antigo);
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
  }
.auth-input::-moz-placeholder {
    color: rgba(245, 243, 238, 0.4);
  }
.auth-input::placeholder {
    color: rgba(245, 243, 238, 0.4);
  }
.auth-password-toggle {
    position: absolute;
    right: 12px;
    background: none;
    border: none;
    color: rgba(245, 243, 238, 0.5);
    cursor: pointer;
    padding: 4px;
    transition: color 0.3s ease;
  }
.auth-password-toggle:hover {
    color: var(--ouro-antigo);
  }
.auth-error {
    background: rgba(231, 76, 60, 0.1);
    border: 1px solid rgba(231, 76, 60, 0.3);
    color: #ff6b6b;
    padding: 12px;
    border-radius: var(--radius-md);
    font-size: 0.9rem;
    text-align: center;
  }
.auth-submit-button {
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    border: none;
    padding: 14px 24px;
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-button);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }
.auth-submit-button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
  }
.auth-submit-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
.auth-form-buttons {
    display: flex;
    gap: 1rem;
  }
.auth-back-button {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--marmore-ivory);
    padding: 14px 24px;
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
  }
.auth-back-button:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.2);
  }
.auth-forgot {
    text-align: center;
  }
.auth-forgot-link {
    color: var(--ouro-antigo);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
  }
.auth-forgot-link:hover {
    color: var(--ouro-claro);
    text-decoration: underline;
  }
.auth-divider {
    position: relative;
    text-align: center;
    margin: 1.5rem 0;
  }
.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
  }
.auth-divider span {
    background: var(--carvao-noturno);
    padding: 0 1rem;
    color: rgba(245, 243, 238, 0.5);
    font-size: 0.9rem;
  }
.auth-signup-link {
    text-align: center;
    color: rgba(245, 243, 238, 0.7);
    font-size: 0.9rem;
  }
.auth-link {
    color: var(--ouro-antigo);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
  }
.auth-link:hover {
    color: var(--ouro-claro);
    text-decoration: underline;
  }
.auth-demo {
    text-align: center;
  }
.auth-demo-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: 1rem;
    -webkit-backdrop-filter: blur(5px);
            backdrop-filter: blur(5px);
  }
.auth-demo-card h3 {
    color: var(--ouro-antigo);
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }
.auth-demo-card p {
    color: rgba(245, 243, 238, 0.7);
    font-size: 0.8rem;
    margin: 0.2rem 0;
    font-family: monospace;
  }
/* Dashboard Styles */
.dashboard {
    min-height: 100vh;
    background: var(--gradient-hero);
    padding-top: 80px;
  }
.dashboard-header {
    background: rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2rem 0;
    margin-bottom: 2rem;
  }
.dashboard-header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
  }
.dashboard-header-user {
    display: flex;
    align-items: center;
    gap: 1rem;
  }
.dashboard-avatar {
    width: 60px;
    height: 60px;
    border: 3px solid var(--ouro-antigo);
  }
.dashboard-avatar-fallback {
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    font-weight: 700;
    font-size: 1.2rem;
  }
.dashboard-header-info h1 {
    margin: 0;
  }
.dashboard-greeting {
    font-size: 1.8rem;
    color: var(--marmore-ivory);
    font-weight: 600;
  }
.dashboard-username {
    color: var(--ouro-antigo);
    font-family: var(--font-titles);
  }
.dashboard-subtitle {
    color: rgba(245, 243, 238, 0.7);
    margin: 0.2rem 0 0 0;
    font-size: 1rem;
  }
.dashboard-header-badges {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }
.dashboard-badge {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--marmore-ivory);
  }
.dashboard-badge-nickname {
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    border: none;
    font-weight: 600;
  }
.dashboard-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem 2rem;
  }
.dashboard-top {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
  }
.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
  }
.dashboard-left {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }
.dashboard-right {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }
/* Balance Card */
.balance-card {
    background: var(--gradient-card);
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-hero);
    -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
    transition: all 0.4s ease;
  }
.balance-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
  }
.balance-card-header {
    padding: 1.5rem 1.5rem 0;
  }
.balance-card-title-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
.balance-card-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--marmore-ivory);
    font-size: 1.1rem;
    margin: 0;
  }
.balance-card-icon {
    color: var(--ouro-antigo);
  }
.balance-toggle {
    background: none;
    border: none;
    color: rgba(245, 243, 238, 0.6);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
  }
.balance-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--ouro-antigo);
  }
.balance-card-content {
    padding: 1rem 1.5rem 1.5rem;
  }
.balance-amount {
    margin-bottom: 1rem;
  }
.balance-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--ouro-antigo);
    font-family: var(--font-titles);
  }
.balance-hidden {
    font-size: 2.5rem;
    font-weight: 700;
    color: rgba(245, 243, 238, 0.5);
    font-family: var(--font-titles);
  }
.balance-trend {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
  }
.balance-trend-icon {
    width: 16px;
    height: 16px;
    color: #22c55e;
  }
.balance-trend-text {
    color: #22c55e;
    font-size: 0.9rem;
    font-weight: 600;
  }
.balance-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
  }
.balance-action-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border-radius: var(--radius-md);
    font-weight: 600;
    transition: all 0.3s ease;
  }
.balance-deposit {
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    border: none;
  }
.balance-deposit:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
  }
.balance-withdraw {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--marmore-ivory);
  }
.balance-withdraw:hover {
    background: rgba(255, 255, 255, 0.2);
  }
.balance-info {
    text-align: center;
  }
.balance-info-text {
    color: rgba(245, 243, 238, 0.6);
    font-size: 0.8rem;
    margin: 0;
  }
/* Quick Actions */
.quick-actions-card {
    background: var(--gradient-card);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-card);
    -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
  }
.quick-actions-header {
    padding: 1.5rem 1.5rem 0;
  }
.quick-actions-title {
    color: var(--marmore-ivory);
    font-size: 1.1rem;
    margin: 0;
  }
.quick-actions-content {
    padding: 1rem 1.5rem 1.5rem;
  }
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 1rem;
  }
.quick-action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    text-decoration: none;
    transition: all 0.3s ease;
    gap: 0.75rem;
  }
.quick-action-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  }
.quick-action-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
.quick-action-content {
    flex: 1;
  }
.quick-action-title {
    color: var(--marmore-ivory);
    font-size: 0.9rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
  }
.quick-action-description {
    color: rgba(245, 243, 238, 0.6);
    font-size: 0.75rem;
    margin: 0;
  }
/* Active Sweepstakes */
.active-sweepstakes-card {
    background: var(--gradient-card);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-card);
    -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
  }
.active-sweepstakes-header {
    padding: 1.5rem 1.5rem 0;
  }
.active-sweepstakes-title-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
.active-sweepstakes-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--marmore-ivory);
    font-size: 1.1rem;
    margin: 0;
  }
.active-sweepstakes-content {
    padding: 1rem 1.5rem 1.5rem;
  }
.active-sweepstakes-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
.sweepstake-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: 1rem;
    transition: all 0.3s ease;
  }
.sweepstake-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
  }
.sweepstake-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75rem;
  }
.sweepstake-info {
    flex: 1;
  }
.sweepstake-title {
    color: var(--marmore-ivory);
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
  }
.sweepstake-badges {
    display: flex;
    gap: 0.5rem;
  }
.sweepstake-badge {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--marmore-ivory);
    font-size: 0.75rem;
  }
.sweepstake-stats {
    display: flex;
    gap: 1rem;
    margin-bottom: 0.75rem;
    flex-wrap: wrap;
  }
.sweepstake-stat {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }
.sweepstake-stat-icon {
    width: 14px;
    height: 14px;
    color: rgba(245, 243, 238, 0.6);
  }
.sweepstake-stat-text {
    color: rgba(245, 243, 238, 0.8);
    font-size: 0.8rem;
  }
.sweepstake-actions {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
  }
.sweepstake-join-btn {
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    border: none;
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
  }
.sweepstake-view-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--marmore-ivory);
    padding: 0.5rem;
  }
.sweepstake-entry {
    text-align: center;
    padding-top: 0.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
.sweepstake-entry-text {
    color: rgba(245, 243, 238, 0.6);
    font-size: 0.8rem;
  }
.no-sweepstakes {
    text-align: center;
    padding: 2rem;
  }
.no-sweepstakes-icon {
    width: 48px;
    height: 48px;
    color: rgba(245, 243, 238, 0.3);
    margin: 0 auto 1rem;
  }
.no-sweepstakes-text {
    color: rgba(245, 243, 238, 0.6);
    margin: 0 0 1rem 0;
  }
.no-sweepstakes-btn {
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
  }
/* Sweepstakes Page */
.sweepstakes-page {
    min-height: 100vh;
    background: var(--gradient-hero);
    padding-top: 80px;
  }
.sweepstakes-header {
    background: rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 3rem 0;
    margin-bottom: 2rem;
  }
.sweepstakes-header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }
.sweepstakes-header-text {
    text-align: center;
    margin-bottom: 3rem;
  }
.sweepstakes-header-title {
    font-family: var(--font-titles);
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
    letter-spacing: 2px;
  }
.sweepstakes-header-subtitle {
    font-size: 1.2rem;
    color: rgba(245, 243, 238, 0.8);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }
.sweepstakes-header-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
  }
.sweepstakes-stat-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
  }
.sweepstakes-stat-card:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
  }
.sweepstakes-stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
  }
.sweepstakes-stat-content {
    flex: 1;
  }
.sweepstakes-stat-value {
    display: block;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--marmore-ivory);
    font-family: var(--font-titles);
  }
.sweepstakes-stat-label {
    display: block;
    font-size: 0.9rem;
    color: rgba(245, 243, 238, 0.7);
    text-transform: uppercase;
    letter-spacing: 1px;
  }
.sweepstakes-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem 2rem;
  }
.sweepstakes-top {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 2rem;
    margin-bottom: 2rem;
  }
.sweepstakes-content {
    flex: 1;
  }
/* Filters */
.sweepstakes-filters {
    background: var(--gradient-card);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
    min-width: 300px;
  }
.sweepstakes-search {
    margin-bottom: 1.5rem;
  }
.sweepstakes-search-container {
    position: relative;
    display: flex;
    align-items: center;
  }
.sweepstakes-search-icon {
    position: absolute;
    left: 12px;
    width: 18px;
    height: 18px;
    color: rgba(245, 243, 238, 0.5);
    z-index: 1;
  }
.sweepstakes-search-input {
    width: 100%;
    padding: 12px 16px 12px 44px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    color: var(--marmore-ivory);
    font-size: 1rem;
  }
.sweepstakes-search-clear {
    position: absolute;
    right: 12px;
    background: none;
    border: none;
    color: rgba(245, 243, 238, 0.5);
    cursor: pointer;
    padding: 4px;
    border-radius: var(--radius-sm);
    transition: all 0.3s ease;
  }
.sweepstakes-search-clear:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--ouro-antigo);
  }
.sweepstakes-filter-title {
    color: var(--marmore-ivory);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
  }
.sweepstakes-categories {
    margin-bottom: 1.5rem;
  }
.sweepstakes-category-grid {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
.sweepstakes-category-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    color: var(--marmore-ivory);
    text-align: left;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
  }
.sweepstakes-category-btn:hover {
    background: rgba(255, 255, 255, 0.1);
  }
.sweepstakes-category-btn.active {
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    border-color: var(--ouro-antigo);
  }
.sweepstakes-prize-ranges {
    margin-bottom: 1.5rem;
  }
.sweepstakes-prize-grid {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
.sweepstakes-prize-badge {
    justify-content: flex-start;
    padding: 0.5rem 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
  }
.sweepstakes-prize-badge.active {
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    border-color: var(--ouro-antigo);
  }
.sweepstakes-additional-filters {
    margin-bottom: 1rem;
  }
.sweepstakes-checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--marmore-ivory);
    cursor: pointer;
  }
.sweepstakes-checkbox {
    width: 16px;
    height: 16px;
    accent-color: var(--ouro-antigo);
  }
.sweepstakes-clear-filters {
    text-align: center;
  }
.sweepstakes-clear-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--marmore-ivory);
  }
/* Create Sweepstake */
.create-sweepstake-container {
    position: relative;
  }
.create-sweepstake-btn {
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    border: none;
    font-weight: 600;
    box-shadow: var(--shadow-button);
    transition: all 0.3s ease;
  }
.create-sweepstake-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
  }
.create-sweepstake-options {
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 1000;
    background: var(--gradient-card);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
    box-shadow: var(--shadow-hero);
    min-width: 400px;
    margin-top: 0.5rem;
  }
.create-sweepstake-options-header {
    text-align: center;
    margin-bottom: 1.5rem;
  }
.create-sweepstake-options-header h3 {
    color: var(--marmore-ivory);
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
  }
.create-sweepstake-options-header p {
    color: rgba(245, 243, 238, 0.7);
    font-size: 0.9rem;
    margin: 0;
  }
.create-sweepstake-grid {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
  }
.create-sweepstake-option {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all 0.3s ease;
  }
.create-sweepstake-option:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
  }
.create-sweepstake-option-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }
.create-sweepstake-option-content {
    flex: 1;
  }
.create-sweepstake-option-title {
    color: var(--marmore-ivory);
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
  }
.create-sweepstake-option-description {
    color: rgba(245, 243, 238, 0.7);
    font-size: 0.8rem;
    margin: 0 0 0.5rem 0;
  }
.create-sweepstake-option-prize {
    color: var(--ouro-antigo);
    font-size: 0.8rem;
    font-weight: 600;
  }
.create-sweepstake-option-arrow {
    width: 24px;
    height: 24px;
    color: rgba(245, 243, 238, 0.5);
    flex-shrink: 0;
  }
.create-sweepstake-options-footer {
    display: flex;
    gap: 1rem;
    justify-content: space-between;
  }
.create-sweepstake-cancel,
  .create-sweepstake-custom {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--marmore-ivory);
  }
.create-sweepstake-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
  }
/* Sweepstakes List */
.sweepstakes-list {
    flex: 1;
  }
.sweepstakes-list-header {
    margin-bottom: 2rem;
  }
.sweepstakes-list-title {
    color: var(--marmore-ivory);
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
  }
.sweepstakes-list-subtitle {
    color: rgba(245, 243, 238, 0.7);
    font-size: 0.9rem;
    margin: 0;
  }
.sweepstakes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }
.sweepstake-card {
    background: var(--gradient-card);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
  }
.sweepstake-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
  }
.sweepstake-card.hot {
    border-color: rgba(255, 165, 0, 0.5);
    box-shadow: 0 0 20px rgba(255, 165, 0, 0.2);
  }
.sweepstake-hot-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: linear-gradient(45deg, #ff6b35, #ff8e53);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 0 var(--radius-xl) 0 var(--radius-lg);
    font-size: 0.7rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    animation: pulse 2s infinite;
  }
.sweepstake-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
  }
.sweepstake-card-title-row {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
  }
.sweepstake-card-type {
    color: var(--ouro-antigo);
  }
.sweepstake-card-title {
    color: var(--marmore-ivory);
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
  }
.sweepstake-card-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
.sweepstake-card-stat {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }
.sweepstake-card-stat-icon {
    width: 14px;
    height: 14px;
    color: rgba(245, 243, 238, 0.6);
  }
.sweepstake-card-stat-text {
    color: rgba(245, 243, 238, 0.8);
    font-size: 0.8rem;
  }
.sweepstake-card-progress {
    margin-bottom: 1rem;
  }
.sweepstake-card-progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 0.5rem;
  }
.sweepstake-card-progress-fill {
    height: 100%;
    background: var(--gradient-gold);
    transition: width 0.3s ease;
  }
.sweepstake-card-progress-text {
    color: rgba(245, 243, 238, 0.6);
    font-size: 0.75rem;
  }
.sweepstake-card-entry {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-md);
  }
.sweepstake-card-entry-label {
    color: rgba(245, 243, 238, 0.7);
    font-size: 0.8rem;
  }
.sweepstake-card-entry-value {
    color: var(--ouro-antigo);
    font-weight: 600;
    font-size: 0.9rem;
  }
.sweepstake-card-actions {
    display: flex;
    gap: 0.75rem;
  }
.sweepstake-card-join-btn {
    flex: 1;
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    border: none;
    font-weight: 600;
  }
.sweepstake-card-view-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--marmore-ivory);
    padding: 0.5rem;
  }
.sweepstakes-load-more {
    text-align: center;
  }
.sweepstakes-load-more-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--marmore-ivory);
    padding: 1rem 2rem;
  }
/* Sweepstake Room Styles */
.sweepstake-page {
    min-height: 100vh;
    background: var(--gradient-hero);
    padding-top: 80px;
  }
.sweepstake-room {
    min-height: calc(100vh - 80px);
  }
.sweepstake-room-header {
    background: rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2rem 0;
    margin-bottom: 2rem;
  }
.sweepstake-room-header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }
.sweepstake-room-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
  }
.sweepstake-back-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--marmore-ivory);
  }
.sweepstake-room-actions {
    display: flex;
    gap: 0.5rem;
  }
.sweepstake-room-title-section {
    margin-bottom: 2rem;
  }
.sweepstake-room-title-main {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 1rem;
  }
.sweepstake-room-title {
    font-family: var(--font-titles);
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    color: var(--marmore-ivory);
    margin: 0;
  }
.sweepstake-room-badges {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }
.sweepstake-type-badge {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--marmore-ivory);
  }
.sweepstake-room-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }
.sweepstake-room-stat {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
  }
.sweepstake-room-stat:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
  }
.sweepstake-room-stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
  }
.sweepstake-room-stat-content {
    flex: 1;
  }
.sweepstake-room-stat-value {
    display: block;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--marmore-ivory);
    font-family: var(--font-titles);
  }
.sweepstake-room-stat-label {
    display: block;
    font-size: 0.9rem;
    color: rgba(245, 243, 238, 0.7);
    text-transform: uppercase;
    letter-spacing: 1px;
  }
.sweepstake-room-progress {
    margin-bottom: 1rem;
  }
.sweepstake-room-progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
  }
.sweepstake-room-progress-fill {
    height: 100%;
    background: var(--gradient-gold);
    transition: width 1s ease;
  }
.sweepstake-room-progress-text {
    display: flex;
    justify-content: space-between;
    color: rgba(245, 243, 238, 0.7);
    font-size: 0.9rem;
  }
.sweepstake-room-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem 2rem;
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 2rem;
  }
.sweepstake-room-main {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }
.sweepstake-room-sidebar {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }
/* Timer Styles */
.sweepstake-timer {
    background: var(--gradient-card);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    padding: 2rem;
    text-align: center;
    -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
    transition: all 0.3s ease;
  }
.sweepstake-timer.urgent {
    border-color: rgba(255, 165, 0, 0.5);
    box-shadow: 0 0 20px rgba(255, 165, 0, 0.2);
  }
.sweepstake-timer.critical {
    border-color: rgba(255, 0, 0, 0.5);
    box-shadow: 0 0 20px rgba(255, 0, 0, 0.3);
    animation: pulse 1s infinite;
  }
.sweepstake-timer-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
  }
.sweepstake-timer-title {
    color: var(--marmore-ivory);
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
  }
.sweepstake-timer-display {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
  }
.sweepstake-timer-unit {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    padding: 1rem;
    min-width: 80px;
    text-align: center;
  }
.sweepstake-timer-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--ouro-antigo);
    font-family: var(--font-titles);
  }
.sweepstake-timer-label {
    display: block;
    font-size: 0.8rem;
    color: rgba(245, 243, 238, 0.7);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-top: 0.5rem;
  }
.sweepstake-timer-separator {
    font-size: 2rem;
    font-weight: 700;
    color: var(--ouro-antigo);
    font-family: var(--font-titles);
  }
.sweepstake-timer-finished {
    text-align: center;
    padding: 2rem;
  }
.sweepstake-timer-finished-title {
    color: var(--ouro-antigo);
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
  }
.sweepstake-timer-finished-text {
    color: rgba(245, 243, 238, 0.8);
    margin: 0;
  }
.sweepstake-timer-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    opacity: 0.3;
    pointer-events: none;
  }
.sweepstake-timer-svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
  }
.sweepstake-timer-ring-bg {
    fill: none;
    stroke: rgba(255, 255, 255, 0.1);
    stroke-width: 2;
  }
.sweepstake-timer-ring-progress {
    fill: none;
    stroke: var(--ouro-antigo);
    stroke-width: 3;
    stroke-linecap: round;
    stroke-dasharray: 283;
    stroke-dashoffset: 283;
  }
/* Info Cards */
.sweepstake-info {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }
.sweepstake-info-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
.sweepstake-info-card {
    background: var(--gradient-card);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
  }
.sweepstake-warning-card {
    border-color: rgba(255, 165, 0, 0.3);
    background: linear-gradient(135deg, rgba(255, 165, 0, 0.1) 0%, rgba(0, 0, 0, 0.3) 100%);
  }
.sweepstake-info-card-header {
    padding: 1.5rem 1.5rem 0;
  }
.sweepstake-info-card-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--marmore-ivory);
    font-size: 1.1rem;
    margin: 0;
  }
.sweepstake-info-card-content {
    padding: 1rem 1.5rem 1.5rem;
  }
.sweepstake-info-description {
    color: rgba(245, 243, 238, 0.8);
    line-height: 1.6;
    margin: 0 0 1.5rem 0;
  }
.sweepstake-info-features {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }
.sweepstake-info-feature {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(245, 243, 238, 0.8);
    font-size: 0.9rem;
  }
.sweepstake-info-rules {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
.sweepstake-info-rule {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
  }
.sweepstake-info-rule-number {
    width: 24px;
    height: 24px;
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 700;
    flex-shrink: 0;
  }
.sweepstake-info-rule-text {
    color: rgba(245, 243, 238, 0.8);
    line-height: 1.5;
    flex: 1;
  }
/* Notification System */
.notification-container {
    position: fixed;
    top: 100px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-width: 400px;
    width: 100%;
  }
.\!notification {
    background: var(--gradient-card) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: var(--radius-lg) !important;
    padding: 1rem !important;
    -webkit-backdrop-filter: blur(10px) !important;
            backdrop-filter: blur(10px) !important;
    box-shadow: var(--shadow-hero) !important;
    display: flex !important;
    align-items: flex-start !important;
    gap: 1rem !important;
    position: relative !important;
    overflow: hidden !important;
  }
.notification {
    background: var(--gradient-card);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: 1rem;
    -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
    box-shadow: var(--shadow-hero);
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    position: relative;
    overflow: hidden;
  }
.\!notification::before {
    content: '' !important;
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    bottom: 0 !important;
    width: 4px !important;
    background: currentColor !important;
  }
.notification::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: currentColor;
  }
.notification-success {
    border-left-color: #10b981;
    color: #10b981;
  }
.notification-error {
    border-left-color: #ef4444;
    color: #ef4444;
  }
.notification-warning {
    border-left-color: #f59e0b;
    color: #f59e0b;
  }
.notification-info {
    border-left-color: #3b82f6;
    color: #3b82f6;
  }
.notification-prize {
    border-left-color: var(--ouro-antigo);
    color: var(--ouro-antigo);
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(0, 0, 0, 0.3) 100%);
    animation: prize-glow 2s ease-in-out infinite alternate;
  }
@keyframes prize-glow {
    from {
      box-shadow: var(--shadow-hero);
    }
    to {
      box-shadow: 0 20px 60px rgba(212, 175, 55, 0.3);
    }
  }
.notification-content {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    flex: 1;
  }
.notification-icon {
    flex-shrink: 0;
    margin-top: 0.125rem;
  }
.notification-text {
    flex: 1;
  }
.notification-title {
    color: var(--marmore-ivory);
    font-size: 0.9rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
  }
.notification-message {
    color: rgba(245, 243, 238, 0.8);
    font-size: 0.8rem;
    line-height: 1.4;
    margin: 0 0 0.5rem 0;
  }
.notification-action {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--marmore-ivory);
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
  }
.notification-action:hover {
    background: rgba(255, 255, 255, 0.2);
  }
.notification-close {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: none;
    border: none;
    color: rgba(245, 243, 238, 0.5);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--radius-sm);
    transition: all 0.3s ease;
  }
.notification-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--marmore-ivory);
  }
/* Responsive adjustments */
@media (max-width: 768px) {
    .notification-container {
      right: 10px;
      left: 10px;
      max-width: none;
    }
  }
/* Live Indicator */
.live-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(231, 76, 60, 0.2);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    color: #E74C3C;
    text-transform: uppercase;
    letter-spacing: 1px;
  }
.live-dot {
    width: 8px;
    height: 8px;
    background: #E74C3C;
    border-radius: 50%;
    animation: pulse-live 2s infinite;
  }
/* Particles Effect */
.particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
  }
/* Glowing effects */
/* Glass morphism */
/* Animations */
/* Custom scrollbar */
.pointer-events-none{
  pointer-events: none;
}
.static{
  position: static;
}
.fixed{
  position: fixed;
}
.absolute{
  position: absolute;
}
.relative{
  position: relative;
}
.inset-0{
  inset: 0px;
}
.-right-1{
  right: -0.25rem;
}
.-top-1{
  top: -0.25rem;
}
.bottom-1\/4{
  bottom: 25%;
}
.bottom-4{
  bottom: 1rem;
}
.left-1\/4{
  left: 25%;
}
.left-2{
  left: 0.5rem;
}
.left-4{
  left: 1rem;
}
.right-1\/4{
  right: 25%;
}
.right-4{
  right: 1rem;
}
.top-1\/4{
  top: 25%;
}
.top-4{
  top: 1rem;
}
.z-10{
  z-index: 10;
}
.z-50{
  z-index: 50;
}
.-mx-1{
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}
.mx-auto{
  margin-left: auto;
  margin-right: auto;
}
.my-1{
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.mb-16{
  margin-bottom: 4rem;
}
.mb-20{
  margin-bottom: 5rem;
}
.mb-3{
  margin-bottom: 0.75rem;
}
.mb-4{
  margin-bottom: 1rem;
}
.ml-1{
  margin-left: 0.25rem;
}
.ml-2{
  margin-left: 0.5rem;
}
.ml-auto{
  margin-left: auto;
}
.mr-1{
  margin-right: 0.25rem;
}
.mr-2{
  margin-right: 0.5rem;
}
.mt-0\.5{
  margin-top: 0.125rem;
}
.mt-1{
  margin-top: 0.25rem;
}
.mt-20{
  margin-top: 5rem;
}
.mt-8{
  margin-top: 2rem;
}
.block{
  display: block;
}
.flex{
  display: flex;
}
.inline-flex{
  display: inline-flex;
}
.table{
  display: table;
}
.grid{
  display: grid;
}
.hidden{
  display: none;
}
.aspect-square{
  aspect-ratio: 1 / 1;
}
.h-10{
  height: 2.5rem;
}
.h-11{
  height: 2.75rem;
}
.h-12{
  height: 3rem;
}
.h-16{
  height: 4rem;
}
.h-2{
  height: 0.5rem;
}
.h-3{
  height: 0.75rem;
}
.h-3\.5{
  height: 0.875rem;
}
.h-4{
  height: 1rem;
}
.h-5{
  height: 1.25rem;
}
.h-6{
  height: 1.5rem;
}
.h-64{
  height: 16rem;
}
.h-8{
  height: 2rem;
}
.h-9{
  height: 2.25rem;
}
.h-96{
  height: 24rem;
}
.h-full{
  height: 100%;
}
.h-px{
  height: 1px;
}
.min-h-screen{
  min-height: 100vh;
}
.w-10{
  width: 2.5rem;
}
.w-12{
  width: 3rem;
}
.w-16{
  width: 4rem;
}
.w-2{
  width: 0.5rem;
}
.w-3{
  width: 0.75rem;
}
.w-3\.5{
  width: 0.875rem;
}
.w-4{
  width: 1rem;
}
.w-5{
  width: 1.25rem;
}
.w-56{
  width: 14rem;
}
.w-6{
  width: 1.5rem;
}
.w-64{
  width: 16rem;
}
.w-8{
  width: 2rem;
}
.w-96{
  width: 24rem;
}
.w-full{
  width: 100%;
}
.min-w-\[8rem\]{
  min-width: 8rem;
}
.max-w-2xl{
  max-width: 42rem;
}
.max-w-sm{
  max-width: 24rem;
}
.flex-1{
  flex: 1 1 0%;
}
.flex-shrink-0{
  flex-shrink: 0;
}
.shrink-0{
  flex-shrink: 0;
}
.transform{
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes pulse{

  50%{
    opacity: .5;
  }
}
.animate-pulse{
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes spin{

  to{
    transform: rotate(360deg);
  }
}
.animate-spin{
  animation: spin 1s linear infinite;
}
.cursor-default{
  cursor: default;
}
.select-none{
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.grid-cols-2{
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-8{
  grid-template-columns: repeat(8, minmax(0, 1fr));
}
.grid-rows-8{
  grid-template-rows: repeat(8, minmax(0, 1fr));
}
.flex-col{
  flex-direction: column;
}
.items-start{
  align-items: flex-start;
}
.items-end{
  align-items: flex-end;
}
.items-center{
  align-items: center;
}
.justify-center{
  justify-content: center;
}
.justify-between{
  justify-content: space-between;
}
.gap-12{
  gap: 3rem;
}
.gap-4{
  gap: 1rem;
}
.gap-6{
  gap: 1.5rem;
}
.space-x-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-8 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-1\.5 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.overflow-hidden{
  overflow: hidden;
}
.whitespace-nowrap{
  white-space: nowrap;
}
.rounded-2xl{
  border-radius: 1rem;
}
.rounded-full{
  border-radius: 9999px;
}
.rounded-lg{
  border-radius: var(--radius);
}
.rounded-md{
  border-radius: calc(var(--radius) - 2px);
}
.rounded-sm{
  border-radius: calc(var(--radius) - 4px);
}
.rounded-t-lg{
  border-top-left-radius: var(--radius);
  border-top-right-radius: var(--radius);
}
.border{
  border-width: 1px;
}
.border-0{
  border-width: 0px;
}
.border-2{
  border-width: 2px;
}
.border-t{
  border-top-width: 1px;
}
.border-blue-500{
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.border-blue-500\/30{
  border-color: rgb(59 130 246 / 0.3);
}
.border-chronos-bronze{
  --tw-border-opacity: 1;
  border-color: rgb(139 111 71 / var(--tw-border-opacity, 1));
}
.border-chronos-bronze\/30{
  border-color: rgb(139 111 71 / 0.3);
}
.border-chronos-gold{
  --tw-border-opacity: 1;
  border-color: rgb(212 175 55 / var(--tw-border-opacity, 1));
}
.border-chronos-gold\/20{
  border-color: rgb(212 175 55 / 0.2);
}
.border-gray-500\/30{
  border-color: rgb(107 114 128 / 0.3);
}
.border-green-500{
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
}
.border-green-500\/30{
  border-color: rgb(34 197 94 / 0.3);
}
.border-input{
  border-color: hsl(var(--input));
}
.border-orange-500{
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}
.border-orange-500\/30{
  border-color: rgb(249 115 22 / 0.3);
}
.border-purple-500\/30{
  border-color: rgb(168 85 247 / 0.3);
}
.border-red-500{
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.border-red-500\/30{
  border-color: rgb(239 68 68 / 0.3);
}
.border-red-600\/30{
  border-color: rgb(220 38 38 / 0.3);
}
.border-transparent{
  border-color: transparent;
}
.border-yellow-500\/30{
  border-color: rgb(234 179 8 / 0.3);
}
.border-t-chronos-gold{
  --tw-border-opacity: 1;
  border-top-color: rgb(212 175 55 / var(--tw-border-opacity, 1));
}
.bg-background{
  background-color: hsl(var(--background));
}
.bg-blue-400\/10{
  background-color: rgb(96 165 250 / 0.1);
}
.bg-blue-50{
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.bg-blue-500{
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.bg-blue-500\/20{
  background-color: rgb(59 130 246 / 0.2);
}
.bg-card{
  background-color: hsl(var(--card));
}
.bg-chronos-charcoal{
  --tw-bg-opacity: 1;
  background-color: rgb(47 47 47 / var(--tw-bg-opacity, 1));
}
.bg-chronos-charcoal\/30{
  background-color: rgb(47 47 47 / 0.3);
}
.bg-chronos-charcoal\/50{
  background-color: rgb(47 47 47 / 0.5);
}
.bg-chronos-gold{
  --tw-bg-opacity: 1;
  background-color: rgb(212 175 55 / var(--tw-bg-opacity, 1));
}
.bg-chronos-gold\/10{
  background-color: rgb(212 175 55 / 0.1);
}
.bg-chronos-gold\/20{
  background-color: rgb(212 175 55 / 0.2);
}
.bg-chronos-gold\/5{
  background-color: rgb(212 175 55 / 0.05);
}
.bg-chronos-violet\/20{
  background-color: rgb(106 72 159 / 0.2);
}
.bg-chronos-violet\/5{
  background-color: rgb(106 72 159 / 0.05);
}
.bg-destructive{
  background-color: hsl(var(--destructive));
}
.bg-emerald-400\/10{
  background-color: rgb(52 211 153 / 0.1);
}
.bg-gray-500{
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.bg-gray-500\/20{
  background-color: rgb(107 114 128 / 0.2);
}
.bg-green-400\/10{
  background-color: rgb(74 222 128 / 0.1);
}
.bg-green-50{
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}
.bg-green-500{
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}
.bg-green-500\/20{
  background-color: rgb(34 197 94 / 0.2);
}
.bg-muted{
  background-color: hsl(var(--muted));
}
.bg-orange-400\/10{
  background-color: rgb(251 146 60 / 0.1);
}
.bg-orange-50{
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));
}
.bg-orange-500{
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}
.bg-orange-500\/20{
  background-color: rgb(249 115 22 / 0.2);
}
.bg-pink-400\/10{
  background-color: rgb(244 114 182 / 0.1);
}
.bg-popover{
  background-color: hsl(var(--popover));
}
.bg-primary{
  background-color: hsl(var(--primary));
}
.bg-purple-400\/10{
  background-color: rgb(192 132 252 / 0.1);
}
.bg-purple-500\/20{
  background-color: rgb(168 85 247 / 0.2);
}
.bg-red-50{
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}
.bg-red-500{
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}
.bg-red-500\/20{
  background-color: rgb(239 68 68 / 0.2);
}
.bg-red-600\/20{
  background-color: rgb(220 38 38 / 0.2);
}
.bg-secondary{
  background-color: hsl(var(--secondary));
}
.bg-white{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-yellow-400\/10{
  background-color: rgb(250 204 21 / 0.1);
}
.bg-yellow-500{
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}
.bg-yellow-500\/20{
  background-color: rgb(234 179 8 / 0.2);
}
.bg-gradient-to-b{
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}
.bg-gradient-to-br{
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.bg-gradient-to-t{
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}
.from-chronos-gold{
  --tw-gradient-from: #D4AF37 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(212 175 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-chronos-ivory\/5{
  --tw-gradient-from: rgb(245 243 238 / 0.05) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(245 243 238 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-chronos-maroon\/20{
  --tw-gradient-from: rgb(123 24 24 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(123 24 24 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.to-chronos-bronze{
  --tw-gradient-to: #8B6F47 var(--tw-gradient-to-position);
}
.to-chronos-charcoal{
  --tw-gradient-to: #2F2F2F var(--tw-gradient-to-position);
}
.to-chronos-violet\/20{
  --tw-gradient-to: rgb(106 72 159 / 0.2) var(--tw-gradient-to-position);
}
.fill-current{
  fill: currentColor;
}
.p-0{
  padding: 0px;
}
.p-1{
  padding: 0.25rem;
}
.p-2{
  padding: 0.5rem;
}
.p-3{
  padding: 0.75rem;
}
.p-4{
  padding: 1rem;
}
.p-6{
  padding: 1.5rem;
}
.p-8{
  padding: 2rem;
}
.px-2{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5{
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4{
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-8{
  padding-left: 2rem;
  padding-right: 2rem;
}
.py-0\.5{
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1{
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5{
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-2{
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-20{
  padding-top: 5rem;
  padding-bottom: 5rem;
}
.py-4{
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.pb-3{
  padding-bottom: 0.75rem;
}
.pl-8{
  padding-left: 2rem;
}
.pr-2{
  padding-right: 0.5rem;
}
.pt-0{
  padding-top: 0px;
}
.pt-2{
  padding-top: 0.5rem;
}
.pt-4{
  padding-top: 1rem;
}
.text-left{
  text-align: left;
}
.text-center{
  text-align: center;
}
.font-display{
  font-family: Cinzel, serif;
}
.font-mono{
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
.font-primary{
  font-family: Raleway, system-ui, sans-serif;
}
.font-titles{
  font-family: Cinzel, serif;
}
.text-2xl{
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl{
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-4xl{
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.text-lg{
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm{
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xs{
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold{
  font-weight: 700;
}
.font-medium{
  font-weight: 500;
}
.font-normal{
  font-weight: 400;
}
.font-semibold{
  font-weight: 600;
}
.leading-none{
  line-height: 1;
}
.tracking-tight{
  letter-spacing: -0.025em;
}
.tracking-widest{
  letter-spacing: 0.1em;
}
.text-blue-400{
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}
.text-blue-500{
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}
.text-card-foreground{
  color: hsl(var(--card-foreground));
}
.text-chronos-bronze{
  --tw-text-opacity: 1;
  color: rgb(139 111 71 / var(--tw-text-opacity, 1));
}
.text-chronos-charcoal{
  --tw-text-opacity: 1;
  color: rgb(47 47 47 / var(--tw-text-opacity, 1));
}
.text-chronos-gold{
  --tw-text-opacity: 1;
  color: rgb(212 175 55 / var(--tw-text-opacity, 1));
}
.text-chronos-ivory{
  --tw-text-opacity: 1;
  color: rgb(245 243 238 / var(--tw-text-opacity, 1));
}
.text-chronos-ivory\/50{
  color: rgb(245 243 238 / 0.5);
}
.text-chronos-ivory\/60{
  color: rgb(245 243 238 / 0.6);
}
.text-chronos-ivory\/70{
  color: rgb(245 243 238 / 0.7);
}
.text-chronos-ivory\/80{
  color: rgb(245 243 238 / 0.8);
}
.text-chronos-violet{
  --tw-text-opacity: 1;
  color: rgb(106 72 159 / var(--tw-text-opacity, 1));
}
.text-destructive-foreground{
  color: hsl(var(--destructive-foreground));
}
.text-emerald-400{
  --tw-text-opacity: 1;
  color: rgb(52 211 153 / var(--tw-text-opacity, 1));
}
.text-foreground{
  color: hsl(var(--foreground));
}
.text-gray-300{
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.text-gray-400{
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.text-gray-500{
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-green-400{
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}
.text-green-500{
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}
.text-muted-foreground{
  color: hsl(var(--muted-foreground));
}
.text-orange-400{
  --tw-text-opacity: 1;
  color: rgb(251 146 60 / var(--tw-text-opacity, 1));
}
.text-orange-500{
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity, 1));
}
.text-pink-400{
  --tw-text-opacity: 1;
  color: rgb(244 114 182 / var(--tw-text-opacity, 1));
}
.text-popover-foreground{
  color: hsl(var(--popover-foreground));
}
.text-primary{
  color: hsl(var(--primary));
}
.text-primary-foreground{
  color: hsl(var(--primary-foreground));
}
.text-purple-400{
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}
.text-purple-500{
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity, 1));
}
.text-red-300{
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}
.text-red-400{
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}
.text-red-500{
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.text-red-600{
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.text-secondary-foreground{
  color: hsl(var(--secondary-foreground));
}
.text-white{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-yellow-400{
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}
.text-yellow-500{
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}
.underline-offset-4{
  text-underline-offset: 4px;
}
.antialiased{
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.opacity-10{
  opacity: 0.1;
}
.opacity-60{
  opacity: 0.6;
}
.shadow-lg{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm{
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline-none{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.outline{
  outline-style: solid;
}
.ring-offset-background{
  --tw-ring-offset-color: hsl(var(--background));
}
.blur-3xl{
  --tw-blur: blur(64px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter{
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur-sm{
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all{
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform{
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-300{
  transition-duration: 300ms;
}
.ease-in{
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
@keyframes enter{

  from{
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}
@keyframes exit{

  to{
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}
.duration-300{
  animation-duration: 300ms;
}
.ease-in{
  animation-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.running{
  animation-play-state: running;
}

/* Reset e Base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Cores Chronos */
  --chronos-maroon: #7B1818;
  --ouro-antigo: #D4AF37;
  --violeta-profundo: #6A489F;
  --marmore-ivory: #F5F3EE;
  --carvao-noturno: #2F2F2F;
  --bronze-suave: #8B6F47;
  --ouro-claro: #F2D472;
  --ouro-escuro: #C79B2F;
  --vermelho-erro: #E74C3C;
  --verde-sucesso: #2ECC71;

  /* Gradientes */
  --gradient-hero: linear-gradient(135deg, var(--carvao-noturno) 0%, #1a1a1a 100%);
  --gradient-gold: linear-gradient(135deg, var(--ouro-antigo) 0%, #f4d03f 100%);
  --gradient-card: linear-gradient(145deg, rgba(47, 47, 47, 0.9) 0%, rgba(123, 24, 24, 0.1) 100%);
  --gradient-maroon: linear-gradient(135deg, var(--chronos-maroon) 0%, #9b2323 100%);
  --gradient-glass: linear-gradient(to right bottom, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));

  /* Tipografia */
  --font-primary: 'Raleway', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-titles: 'Cinzel', serif;

  /* Sombras */
  --shadow-card: 0 10px 30px rgba(0, 0, 0, 0.3);
  --shadow-button: 0 4px 15px rgba(212, 175, 55, 0.3);
  --shadow-hero: 0 20px 60px rgba(0, 0, 0, 0.5);
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 40px rgba(0, 0, 0, 0.5);

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 20px;

  /* Transitions */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;

  /* Z-index layers */
  --z-back: -1;
  --z-normal: 1;
  --z-front: 10;
  --z-modal: 100;
  --z-toast: 200;
  --z-tooltip: 300;
  --z-header: 1000;

  /* Shadcn Variables */
  --background: 47 47 47; /* Carvão Noturno */
  --foreground: 245 243 238; /* Mármore Ívory */
  --card: 47 47 47;
  --card-foreground: 245 243 238;
  --popover: 47 47 47;
  --popover-foreground: 245 243 238;
  --primary: 123 24 24; /* Chronos Maroon */
  --primary-foreground: 245 243 238;
  --secondary: 212 175 55; /* Ouro Antigo */
  --secondary-foreground: 47 47 47;
  --muted: 139 111 71; /* Bronze Suave */
  --muted-foreground: 245 243 238;
  --accent: 106 72 159; /* Violeta Profundo */
  --accent-foreground: 245 243 238;
  --destructive: 220 38 38;
  --destructive-foreground: 248 250 252;
  --border: 139 111 71;
  --input: 47 47 47;
  --ring: 212 175 55;
  --radius: 0.5rem;
}

html {
  scroll-behavior: smooth;
  overflow-x: hidden;
}

body {
  font-family: var(--font-primary);
  line-height: 1.6;
  color: var(--marmore-ivory);
  background: var(--carvao-noturno);
  overflow-x: hidden;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
}

/* Keyframes */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-live {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes floatLeft {
  0% {
    transform: translateX(-100px) translateY(0) rotate(-10deg);
  }
  100% {
    transform: translateX(-80px) translateY(-20px) rotate(-5deg);
  }
}

@keyframes floatRight {
  0% {
    transform: translateX(100px) translateY(0) rotate(10deg);
  }
  100% {
    transform: translateX(80px) translateY(-15px) rotate(5deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }

  .hero-statue {
    width: 120px;
    height: 240px;
  }

  .stat-card {
    padding: 15px;
  }

  .stat-value {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .hero-statue {
    display: none;
  }

  .stat-card {
    padding: 12px;
  }

  .stat-value {
    font-size: 1.2rem;
  }
}

.file\:border-0::file-selector-button{
  border-width: 0px;
}

.file\:bg-transparent::file-selector-button{
  background-color: transparent;
}

.file\:text-sm::file-selector-button{
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.file\:font-medium::file-selector-button{
  font-weight: 500;
}

.placeholder\:text-muted-foreground::-moz-placeholder{
  color: hsl(var(--muted-foreground));
}

.placeholder\:text-muted-foreground::placeholder{
  color: hsl(var(--muted-foreground));
}

.hover\:scale-105:hover{
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:border-chronos-gold\/50:hover{
  border-color: rgb(212 175 55 / 0.5);
}

.hover\:bg-accent:hover{
  background-color: hsl(var(--accent));
}

.hover\:bg-chronos-bronze:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(139 111 71 / var(--tw-bg-opacity, 1));
}

.hover\:bg-chronos-gold:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(212 175 55 / var(--tw-bg-opacity, 1));
}

.hover\:bg-destructive\/80:hover{
  background-color: hsl(var(--destructive) / 0.8);
}

.hover\:bg-destructive\/90:hover{
  background-color: hsl(var(--destructive) / 0.9);
}

.hover\:bg-primary\/80:hover{
  background-color: hsl(var(--primary) / 0.8);
}

.hover\:bg-primary\/90:hover{
  background-color: hsl(var(--primary) / 0.9);
}

.hover\:bg-secondary\/80:hover{
  background-color: hsl(var(--secondary) / 0.8);
}

.hover\:text-accent-foreground:hover{
  color: hsl(var(--accent-foreground));
}

.hover\:text-chronos-charcoal:hover{
  --tw-text-opacity: 1;
  color: rgb(47 47 47 / var(--tw-text-opacity, 1));
}

.hover\:underline:hover{
  text-decoration-line: underline;
}

.focus\:bg-accent:focus{
  background-color: hsl(var(--accent));
}

.focus\:text-accent-foreground:focus{
  color: hsl(var(--accent-foreground));
}

.focus\:outline-none:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-2:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-ring:focus{
  --tw-ring-color: hsl(var(--ring));
}

.focus\:ring-offset-2:focus{
  --tw-ring-offset-width: 2px;
}

.focus-visible\:outline-none:focus-visible{
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-visible\:ring-2:focus-visible{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-ring:focus-visible{
  --tw-ring-color: hsl(var(--ring));
}

.focus-visible\:ring-offset-2:focus-visible{
  --tw-ring-offset-width: 2px;
}

.disabled\:pointer-events-none:disabled{
  pointer-events: none;
}

.disabled\:cursor-not-allowed:disabled{
  cursor: not-allowed;
}

.disabled\:opacity-50:disabled{
  opacity: 0.5;
}

.group:hover .group-hover\:bg-chronos-charcoal\/70{
  background-color: rgb(47 47 47 / 0.7);
}

.group:hover .group-hover\:bg-chronos-gold\/20{
  background-color: rgb(212 175 55 / 0.2);
}

.group:hover .group-hover\:text-chronos-gold{
  --tw-text-opacity: 1;
  color: rgb(212 175 55 / var(--tw-text-opacity, 1));
}

.peer:disabled ~ .peer-disabled\:cursor-not-allowed{
  cursor: not-allowed;
}

.peer:disabled ~ .peer-disabled\:opacity-70{
  opacity: 0.7;
}

.data-\[disabled\]\:pointer-events-none[data-disabled]{
  pointer-events: none;
}

.data-\[state\=open\]\:bg-accent[data-state="open"]{
  background-color: hsl(var(--accent));
}

.data-\[disabled\]\:opacity-50[data-disabled]{
  opacity: 0.5;
}

.data-\[state\=open\]\:animate-in[data-state="open"]{
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}

.data-\[state\=closed\]\:animate-out[data-state="closed"]{
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}

.data-\[state\=closed\]\:fade-out-0[data-state="closed"]{
  --tw-exit-opacity: 0;
}

.data-\[state\=open\]\:fade-in-0[data-state="open"]{
  --tw-enter-opacity: 0;
}

.data-\[state\=closed\]\:zoom-out-95[data-state="closed"]{
  --tw-exit-scale: .95;
}

.data-\[state\=open\]\:zoom-in-95[data-state="open"]{
  --tw-enter-scale: .95;
}

.data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"]{
  --tw-enter-translate-y: -0.5rem;
}

.data-\[side\=left\]\:slide-in-from-right-2[data-side="left"]{
  --tw-enter-translate-x: 0.5rem;
}

.data-\[side\=right\]\:slide-in-from-left-2[data-side="right"]{
  --tw-enter-translate-x: -0.5rem;
}

.data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"]{
  --tw-enter-translate-y: 0.5rem;
}

.dark\:bg-blue-900\/20:is(.dark *){
  background-color: rgb(30 58 138 / 0.2);
}

.dark\:bg-chronos-charcoal:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(47 47 47 / var(--tw-bg-opacity, 1));
}

.dark\:bg-green-900\/20:is(.dark *){
  background-color: rgb(20 83 45 / 0.2);
}

.dark\:bg-orange-900\/20:is(.dark *){
  background-color: rgb(124 45 18 / 0.2);
}

.dark\:bg-red-900\/20:is(.dark *){
  background-color: rgb(127 29 29 / 0.2);
}

@media (min-width: 640px){

  .sm\:flex{
    display: flex;
  }
}

@media (min-width: 768px){

  .md\:hidden{
    display: none;
  }

  .md\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:text-5xl{
    font-size: 3rem;
    line-height: 1;
  }
}

@media (min-width: 1024px){

  .lg\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[11].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Raleway","arguments":[{"subsets":["latin"],"variable":"--font-raleway","weight":["300","400","500","600","700","800"]}],"variableName":"raleway"} ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic-ext */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/200388358b398524-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/34900c74a84112b6-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/10dadb2e82d03733-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/5f2068c3133468f5-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/9bf67a161a796382-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/200388358b398524-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/34900c74a84112b6-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/10dadb2e82d03733-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/5f2068c3133468f5-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/9bf67a161a796382-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/_next/static/media/200388358b398524-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/_next/static/media/34900c74a84112b6-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/_next/static/media/10dadb2e82d03733-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/_next/static/media/5f2068c3133468f5-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/_next/static/media/9bf67a161a796382-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(/_next/static/media/200388358b398524-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(/_next/static/media/34900c74a84112b6-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(/_next/static/media/10dadb2e82d03733-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(/_next/static/media/5f2068c3133468f5-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(/_next/static/media/9bf67a161a796382-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(/_next/static/media/200388358b398524-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(/_next/static/media/34900c74a84112b6-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(/_next/static/media/10dadb2e82d03733-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(/_next/static/media/5f2068c3133468f5-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(/_next/static/media/9bf67a161a796382-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url(/_next/static/media/200388358b398524-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url(/_next/static/media/34900c74a84112b6-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url(/_next/static/media/10dadb2e82d03733-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url(/_next/static/media/5f2068c3133468f5-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Raleway_90c869';
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url(/_next/static/media/9bf67a161a796382-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: '__Raleway_Fallback_90c869';src: local("Arial");ascent-override: 89.62%;descent-override: 22.31%;line-gap-override: 0.00%;size-adjust: 104.89%
}.__className_90c869 {font-family: '__Raleway_90c869', '__Raleway_Fallback_90c869';font-style: normal
}.__variable_90c869 {--font-raleway: '__Raleway_90c869', '__Raleway_Fallback_90c869'
}

/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[11].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Cinzel","arguments":[{"subsets":["latin"],"variable":"--font-cinzel","weight":["400","500","600","700","800"]}],"variableName":"cinzel"} ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* latin-ext */
@font-face {
  font-family: '__Cinzel_94db00';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/2fb380e60212dc49-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Cinzel_94db00';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/42f131834de1b4dc-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: '__Cinzel_94db00';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/_next/static/media/2fb380e60212dc49-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Cinzel_94db00';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/_next/static/media/42f131834de1b4dc-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: '__Cinzel_94db00';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(/_next/static/media/2fb380e60212dc49-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Cinzel_94db00';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(/_next/static/media/42f131834de1b4dc-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: '__Cinzel_94db00';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(/_next/static/media/2fb380e60212dc49-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Cinzel_94db00';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(/_next/static/media/42f131834de1b4dc-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: '__Cinzel_94db00';
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url(/_next/static/media/2fb380e60212dc49-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Cinzel_94db00';
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url(/_next/static/media/42f131834de1b4dc-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: '__Cinzel_Fallback_94db00';src: local("Times New Roman");ascent-override: 69.57%;descent-override: 26.52%;line-gap-override: 0.00%;size-adjust: 140.28%
}.__className_94db00 {font-family: '__Cinzel_94db00', '__Cinzel_Fallback_94db00';font-style: normal
}.__variable_94db00 {--font-cinzel: '__Cinzel_94db00', '__Cinzel_Fallback_94db00'
}

