@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700;800&family=Raleway:wght@300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Reset e Base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Cores Chronos */
  --chronos-maroon: #7B1818;
  --ouro-antigo: #D4AF37;
  --violeta-profundo: #6A489F;
  --marmore-ivory: #F5F3EE;
  --carvao-noturno: #2F2F2F;
  --bronze-suave: #8B6F47;
  --ouro-claro: #F2D472;
  --ouro-escuro: #C79B2F;
  --vermelho-erro: #E74C3C;
  --verde-sucesso: #2ECC71;

  /* Gradientes */
  --gradient-hero: linear-gradient(135deg, var(--carvao-noturno) 0%, #1a1a1a 100%);
  --gradient-gold: linear-gradient(135deg, var(--ouro-antigo) 0%, #f4d03f 100%);
  --gradient-card: linear-gradient(145deg, rgba(47, 47, 47, 0.9) 0%, rgba(123, 24, 24, 0.1) 100%);
  --gradient-maroon: linear-gradient(135deg, var(--chronos-maroon) 0%, #9b2323 100%);
  --gradient-glass: linear-gradient(to right bottom, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));

  /* Tipografia */
  --font-primary: 'Raleway', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-titles: 'Cinzel', serif;

  /* Sombras */
  --shadow-card: 0 10px 30px rgba(0, 0, 0, 0.3);
  --shadow-button: 0 4px 15px rgba(212, 175, 55, 0.3);
  --shadow-hero: 0 20px 60px rgba(0, 0, 0, 0.5);
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 40px rgba(0, 0, 0, 0.5);

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 20px;

  /* Transitions */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;

  /* Z-index layers */
  --z-back: -1;
  --z-normal: 1;
  --z-front: 10;
  --z-modal: 100;
  --z-toast: 200;
  --z-tooltip: 300;
  --z-header: 1000;

  /* Shadcn Variables */
  --background: 47 47 47; /* Carvão Noturno */
  --foreground: 245 243 238; /* Mármore Ívory */
  --card: 47 47 47;
  --card-foreground: 245 243 238;
  --popover: 47 47 47;
  --popover-foreground: 245 243 238;
  --primary: 123 24 24; /* Chronos Maroon */
  --primary-foreground: 245 243 238;
  --secondary: 212 175 55; /* Ouro Antigo */
  --secondary-foreground: 47 47 47;
  --muted: 139 111 71; /* Bronze Suave */
  --muted-foreground: 245 243 238;
  --accent: 106 72 159; /* Violeta Profundo */
  --accent-foreground: 245 243 238;
  --destructive: 220 38 38;
  --destructive-foreground: 248 250 252;
  --border: 139 111 71;
  --input: 47 47 47;
  --ring: 212 175 55;
  --radius: 0.5rem;
}

html {
  scroll-behavior: smooth;
  overflow-x: hidden;
}

body {
  font-family: var(--font-primary);
  line-height: 1.6;
  color: var(--marmore-ivory);
  background: var(--carvao-noturno);
  overflow-x: hidden;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* Chronos specific styles */
  .chronos-gradient {
    background: var(--gradient-hero);
  }

  .chronos-gold-gradient {
    background: var(--gradient-gold);
  }

  .chronos-card {
    background: var(--gradient-card);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-card);
    backdrop-filter: blur(5px);
    transition: all 0.4s ease;
  }

  .chronos-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
    border-color: rgba(212, 175, 55, 0.3);
  }

  .chronos-button-primary {
    background: var(--gradient-maroon);
    color: var(--marmore-ivory);
    border: none;
    padding: 12px 24px;
    border-radius: var(--radius-md);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-button);
    font-family: var(--font-primary);
  }

  .chronos-button-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(123, 24, 24, 0.4);
  }

  .chronos-button-gold {
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    border: none;
    padding: 12px 24px;
    border-radius: var(--radius-md);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-button);
    font-family: var(--font-primary);
  }

  .chronos-button-gold:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
  }

  .chronos-text-gold {
    color: var(--ouro-antigo);
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .chronos-text-gradient {
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-family: var(--font-titles);
    font-weight: 700;
    letter-spacing: 2px;
  }

  /* Header Styles */
  .header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(245, 243, 238, 0.95);
    backdrop-filter: blur(10px);
    z-index: var(--z-header);
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .header.scrolled {
    background: rgba(47, 47, 47, 0.95);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
  }

  .nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
  }

  .nav-logo {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .logo {
    height: 40px;
    width: auto;
    transition: var(--transition-normal);
  }

  .logo:hover {
    transform: rotate(10deg);
  }

  .logo-text {
    font-family: var(--font-titles);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--chronos-maroon);
    letter-spacing: 3px;
    transition: var(--transition-normal);
    text-transform: uppercase;
    text-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }

  .nav-menu {
    display: flex;
    align-items: center;
    gap: 2rem;
  }

  .nav-link {
    text-decoration: none;
    color: var(--carvao-noturno);
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
  }

  .nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -4px;
    left: 0;
    background: var(--chronos-maroon);
    transition: width 0.3s ease;
  }

  .nav-link:hover::after,
  .nav-link:focus::after {
    width: 100%;
  }

  .nav-link:hover {
    color: var(--chronos-maroon);
  }

  .btn-login {
    background: transparent;
    border: 2px solid var(--chronos-maroon);
    color: var(--chronos-maroon);
    padding: 8px 20px;
    border-radius: var(--radius-md);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .btn-login:hover {
    background: var(--chronos-maroon);
    color: white;
    transform: translateY(-2px);
  }

  .btn-primary {
    background: var(--gradient-gold);
    border: none;
    color: var(--carvao-noturno);
    padding: 10px 24px;
    border-radius: var(--radius-md);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-button);
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
  }

  /* Hero Section */
  .hero {
    min-height: 100vh;
    background: var(--gradient-hero);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    padding-top: 80px;
  }

  .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.1;
    z-index: var(--z-back);
  }

  .hero-statue {
    position: absolute;
    width: 300px;
    height: 400px;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M50 10 L60 30 L50 50 L40 30 Z" fill="%23D4AF37" opacity="0.3"/></svg>') no-repeat center;
    background-size: contain;
    transition: all 5s ease-in-out;
  }

  .left-statue {
    left: -100px;
    top: 20%;
    transform: rotate(-10deg);
    animation: floatLeft 8s ease-in-out infinite alternate;
  }

  .right-statue {
    right: -100px;
    top: 30%;
    transform: rotate(10deg);
    animation: floatRight 10s ease-in-out infinite alternate;
  }

  .hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    text-align: center;
    z-index: var(--z-normal);
    position: relative;
  }

  .hero-content {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 1s ease, transform 1s ease;
  }

  .hero-title {
    margin-bottom: 2rem;
  }

  .hero-title-main {
    display: block;
    font-family: var(--font-titles);
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: 700;
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: 8px;
    text-shadow: 0 5px 20px rgba(0, 0, 0, 0.3), 0 2px 5px rgba(212, 175, 55, 0.2);
    opacity: 1;
    text-transform: uppercase;
    animation: fadeInUp 1s ease 0.2s forwards;
  }

  .hero-title-sub {
    display: block;
    font-family: var(--font-titles);
    font-size: clamp(1.5rem, 4vw, 2.5rem);
    font-weight: 500;
    color: var(--marmore-ivory);
    letter-spacing: 10px;
    margin-top: 0.8rem;
    animation: fadeInUp 1s ease 0.5s forwards;
    opacity: 1;
    text-transform: uppercase;
  }

  .hero-description {
    max-width: 700px;
    margin: 0 auto 3rem;
    font-size: clamp(1rem, 2vw, 1.25rem);
    color: rgba(245, 243, 238, 0.9);
    animation: fadeIn 1s ease 0.8s forwards;
    opacity: 1;
  }

  .hero-stats {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
  }

  .hero-cta {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-top: 2rem;
    animation: fadeIn 1s ease 1.2s forwards;
    opacity: 1;
  }

  .btn-hero-primary {
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    border: none;
    padding: 15px 30px;
    border-radius: var(--radius-md);
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-button);
  }

  .btn-hero-primary:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(212, 175, 55, 0.5);
  }

  .btn-hero-secondary {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--marmore-ivory);
    padding: 15px 30px;
    border-radius: var(--radius-md);
    font-size: 1.1rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
  }

  .btn-hero-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  }

  .hero-scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 1.5rem;
    color: var(--marmore-ivory);
    cursor: pointer;
    animation: bounce 2s infinite;
    opacity: 0.7;
    transition: opacity 0.3s ease;
  }

  .hero-scroll-indicator:hover {
    opacity: 1;
  }

  /* Stat Cards */
  .stat-card {
    background: var(--gradient-card);
    border-radius: var(--radius-lg);
    padding: 20px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.4s ease;
    box-shadow: var(--shadow-card);
  }

  .stat-card:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.4);
    border-color: rgba(212, 175, 55, 0.3);
  }

  .stat-icon {
    font-size: 2rem;
    margin-bottom: 10px;
  }

  .stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--ouro-antigo);
    font-family: var(--font-titles);
    margin-bottom: 5px;
  }

  .stat-label {
    font-size: 0.9rem;
    color: rgba(245, 243, 238, 0.7);
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  /* Current Sweepstakes Section */
  .current-sweepstakes {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--carvao-noturno) 0%, #1a1a1a 100%);
    position: relative;
  }

  .section-header {
    text-align: center;
    margin-bottom: 4rem;
  }

  .section-title {
    font-family: var(--font-titles);
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
    letter-spacing: 2px;
  }

  .section-subtitle {
    font-size: 1.2rem;
    color: rgba(245, 243, 238, 0.8);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }

  .sweepstakes-card {
    max-width: 800px;
    margin: 0 auto;
    background: var(--gradient-card);
    border-radius: var(--radius-xl);
    padding: 3rem;
    box-shadow: var(--shadow-hero);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.4s ease;
  }

  .sweepstakes-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 30px 80px rgba(0, 0, 0, 0.6);
  }

  .sweepstakes-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .sweepstakes-header h2 {
    font-family: var(--font-titles);
    font-size: 2rem;
    color: var(--marmore-ivory);
    margin: 0;
    letter-spacing: 1px;
  }

  .sweepstakes-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
  }

  .sweepstakes-stat {
    text-align: center;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
  }

  .sweepstakes-stat:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-5px);
  }

  .stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--ouro-antigo);
    font-family: var(--font-titles);
    margin-bottom: 0.5rem;
  }

  .stat-text {
    font-size: 0.9rem;
    color: rgba(245, 243, 238, 0.7);
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  .sweepstakes-timer {
    text-align: center;
    margin-bottom: 2rem;
  }

  .timer-label {
    display: block;
    font-size: 1rem;
    color: rgba(245, 243, 238, 0.8);
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  .timer {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .timer-unit {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    padding: 1rem;
    min-width: 80px;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .timer-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--ouro-antigo);
    font-family: var(--font-titles);
  }

  .timer-text {
    display: block;
    font-size: 0.8rem;
    color: rgba(245, 243, 238, 0.7);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-top: 0.5rem;
  }

  .btn-sweepstakes {
    width: 100%;
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--radius-md);
    font-size: 1.2rem;
    font-weight: 700;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-button);
    margin-bottom: 2rem;
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  .btn-sweepstakes:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(212, 175, 55, 0.5);
  }

  .sweepstakes-participants {
    text-align: center;
  }

  .participants-avatars {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }

  .participant-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
    border: 2px solid rgba(255, 255, 255, 0.2);
  }

  .participant-count {
    font-size: 0.9rem;
    color: var(--ouro-antigo);
    font-weight: 600;
    margin-left: 0.5rem;
  }

  .participants-message {
    font-size: 0.9rem;
    color: rgba(245, 243, 238, 0.7);
    margin: 0;
  }

  /* Authentication Pages */
  .auth-page {
    min-height: 100vh;
    background: var(--gradient-hero);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    padding: 2rem 1rem;
  }

  .auth-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.1;
    z-index: var(--z-back);
  }

  .auth-container {
    max-width: 500px;
    width: 100%;
    z-index: var(--z-normal);
    position: relative;
  }

  .auth-back {
    margin-bottom: 2rem;
  }

  .auth-back-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(245, 243, 238, 0.7);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
  }

  .auth-back-link:hover {
    color: var(--ouro-antigo);
  }

  .auth-logo {
    text-align: center;
    margin-bottom: 2rem;
  }

  .auth-logo-text {
    font-family: var(--font-titles);
    font-size: 2rem;
    font-weight: 700;
    color: var(--ouro-antigo);
    letter-spacing: 3px;
    margin-top: 0.5rem;
    text-transform: uppercase;
  }

  .auth-progress {
    margin-bottom: 2rem;
  }

  .auth-progress-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 1rem;
  }

  .auth-progress-fill {
    height: 100%;
    background: var(--gradient-gold);
    transition: width 0.3s ease;
  }

  .auth-progress-steps {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: rgba(245, 243, 238, 0.6);
  }

  .auth-progress-steps span.active {
    color: var(--ouro-antigo);
    font-weight: 600;
  }

  .auth-card-container {
    margin-bottom: 2rem;
  }

  .auth-card {
    background: var(--gradient-card);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-hero);
    backdrop-filter: blur(10px);
  }

  .auth-card-header {
    text-align: center;
    padding: 2rem 2rem 1rem;
  }

  .auth-card-title {
    font-family: var(--font-titles);
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--marmore-ivory);
    margin-bottom: 0.5rem;
  }

  .auth-card-description {
    color: rgba(245, 243, 238, 0.7);
    font-size: 1rem;
    line-height: 1.5;
  }

  .auth-card-content {
    padding: 1rem 2rem 2rem;
  }

  .auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .auth-field {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .auth-label {
    font-weight: 600;
    color: var(--marmore-ivory);
    font-size: 0.9rem;
  }

  .auth-input-container {
    position: relative;
    display: flex;
    align-items: center;
  }

  .auth-input-icon {
    position: absolute;
    left: 12px;
    width: 18px;
    height: 18px;
    color: rgba(245, 243, 238, 0.5);
    z-index: 1;
  }

  .auth-input {
    width: 100%;
    padding: 12px 16px 12px 44px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    color: var(--marmore-ivory);
    font-size: 1rem;
    transition: all 0.3s ease;
  }

  .auth-input:focus {
    outline: none;
    border-color: var(--ouro-antigo);
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
  }

  .auth-input::placeholder {
    color: rgba(245, 243, 238, 0.4);
  }

  .auth-password-toggle {
    position: absolute;
    right: 12px;
    background: none;
    border: none;
    color: rgba(245, 243, 238, 0.5);
    cursor: pointer;
    padding: 4px;
    transition: color 0.3s ease;
  }

  .auth-password-toggle:hover {
    color: var(--ouro-antigo);
  }

  .auth-error {
    background: rgba(231, 76, 60, 0.1);
    border: 1px solid rgba(231, 76, 60, 0.3);
    color: #ff6b6b;
    padding: 12px;
    border-radius: var(--radius-md);
    font-size: 0.9rem;
    text-align: center;
  }

  .auth-submit-button {
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    border: none;
    padding: 14px 24px;
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-button);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .auth-submit-button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
  }

  .auth-submit-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }

  .auth-form-buttons {
    display: flex;
    gap: 1rem;
  }

  .auth-back-button {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--marmore-ivory);
    padding: 14px 24px;
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .auth-back-button:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.2);
  }

  .auth-forgot {
    text-align: center;
  }

  .auth-forgot-link {
    color: var(--ouro-antigo);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
  }

  .auth-forgot-link:hover {
    color: var(--ouro-claro);
    text-decoration: underline;
  }

  .auth-divider {
    position: relative;
    text-align: center;
    margin: 1.5rem 0;
  }

  .auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
  }

  .auth-divider span {
    background: var(--carvao-noturno);
    padding: 0 1rem;
    color: rgba(245, 243, 238, 0.5);
    font-size: 0.9rem;
  }

  .auth-signup-link {
    text-align: center;
    color: rgba(245, 243, 238, 0.7);
    font-size: 0.9rem;
  }

  .auth-link {
    color: var(--ouro-antigo);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
  }

  .auth-link:hover {
    color: var(--ouro-claro);
    text-decoration: underline;
  }

  .auth-demo {
    text-align: center;
  }

  .auth-demo-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: 1rem;
    backdrop-filter: blur(5px);
  }

  .auth-demo-card h3 {
    color: var(--ouro-antigo);
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .auth-demo-card p {
    color: rgba(245, 243, 238, 0.7);
    font-size: 0.8rem;
    margin: 0.2rem 0;
    font-family: monospace;
  }

  /* Dashboard Styles */
  .dashboard {
    min-height: 100vh;
    background: var(--gradient-hero);
    padding-top: 80px;
  }

  .dashboard-header {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2rem 0;
    margin-bottom: 2rem;
  }

  .dashboard-header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .dashboard-header-user {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .dashboard-avatar {
    width: 60px;
    height: 60px;
    border: 3px solid var(--ouro-antigo);
  }

  .dashboard-avatar-fallback {
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    font-weight: 700;
    font-size: 1.2rem;
  }

  .dashboard-header-info h1 {
    margin: 0;
  }

  .dashboard-greeting {
    font-size: 1.8rem;
    color: var(--marmore-ivory);
    font-weight: 600;
  }

  .dashboard-username {
    color: var(--ouro-antigo);
    font-family: var(--font-titles);
  }

  .dashboard-subtitle {
    color: rgba(245, 243, 238, 0.7);
    margin: 0.2rem 0 0 0;
    font-size: 1rem;
  }

  .dashboard-header-badges {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .dashboard-badge {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--marmore-ivory);
  }

  .dashboard-badge-nickname {
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    border: none;
    font-weight: 600;
  }

  .dashboard-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem 2rem;
  }

  .dashboard-top {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
  }

  .dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
  }

  .dashboard-left {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  .dashboard-right {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  /* Balance Card */
  .balance-card {
    background: var(--gradient-card);
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-hero);
    backdrop-filter: blur(10px);
    transition: all 0.4s ease;
  }

  .balance-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
  }

  .balance-card-header {
    padding: 1.5rem 1.5rem 0;
  }

  .balance-card-title-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .balance-card-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--marmore-ivory);
    font-size: 1.1rem;
    margin: 0;
  }

  .balance-card-icon {
    color: var(--ouro-antigo);
  }

  .balance-toggle {
    background: none;
    border: none;
    color: rgba(245, 243, 238, 0.6);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
  }

  .balance-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--ouro-antigo);
  }

  .balance-card-content {
    padding: 1rem 1.5rem 1.5rem;
  }

  .balance-amount {
    margin-bottom: 1rem;
  }

  .balance-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--ouro-antigo);
    font-family: var(--font-titles);
  }

  .balance-hidden {
    font-size: 2.5rem;
    font-weight: 700;
    color: rgba(245, 243, 238, 0.5);
    font-family: var(--font-titles);
  }

  .balance-trend {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
  }

  .balance-trend-icon {
    width: 16px;
    height: 16px;
    color: #22c55e;
  }

  .balance-trend-text {
    color: #22c55e;
    font-size: 0.9rem;
    font-weight: 600;
  }

  .balance-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .balance-action-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border-radius: var(--radius-md);
    font-weight: 600;
    transition: all 0.3s ease;
  }

  .balance-deposit {
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    border: none;
  }

  .balance-deposit:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
  }

  .balance-withdraw {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--marmore-ivory);
  }

  .balance-withdraw:hover {
    background: rgba(255, 255, 255, 0.2);
  }

  .balance-info {
    text-align: center;
  }

  .balance-info-text {
    color: rgba(245, 243, 238, 0.6);
    font-size: 0.8rem;
    margin: 0;
  }

  /* Quick Actions */
  .quick-actions-card {
    background: var(--gradient-card);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-card);
    backdrop-filter: blur(10px);
  }

  .quick-actions-header {
    padding: 1.5rem 1.5rem 0;
  }

  .quick-actions-title {
    color: var(--marmore-ivory);
    font-size: 1.1rem;
    margin: 0;
  }

  .quick-actions-content {
    padding: 1rem 1.5rem 1.5rem;
  }

  .quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 1rem;
  }

  .quick-action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    text-decoration: none;
    transition: all 0.3s ease;
    gap: 0.75rem;
  }

  .quick-action-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  }

  .quick-action-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .quick-action-content {
    flex: 1;
  }

  .quick-action-title {
    color: var(--marmore-ivory);
    font-size: 0.9rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
  }

  .quick-action-description {
    color: rgba(245, 243, 238, 0.6);
    font-size: 0.75rem;
    margin: 0;
  }

  /* Active Sweepstakes */
  .active-sweepstakes-card {
    background: var(--gradient-card);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-card);
    backdrop-filter: blur(10px);
  }

  .active-sweepstakes-header {
    padding: 1.5rem 1.5rem 0;
  }

  .active-sweepstakes-title-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .active-sweepstakes-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--marmore-ivory);
    font-size: 1.1rem;
    margin: 0;
  }

  .active-sweepstakes-content {
    padding: 1rem 1.5rem 1.5rem;
  }

  .active-sweepstakes-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .sweepstake-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: 1rem;
    transition: all 0.3s ease;
  }

  .sweepstake-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
  }

  .sweepstake-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75rem;
  }

  .sweepstake-info {
    flex: 1;
  }

  .sweepstake-title {
    color: var(--marmore-ivory);
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
  }

  .sweepstake-badges {
    display: flex;
    gap: 0.5rem;
  }

  .sweepstake-badge {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--marmore-ivory);
    font-size: 0.75rem;
  }

  .sweepstake-stats {
    display: flex;
    gap: 1rem;
    margin-bottom: 0.75rem;
    flex-wrap: wrap;
  }

  .sweepstake-stat {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }

  .sweepstake-stat-icon {
    width: 14px;
    height: 14px;
    color: rgba(245, 243, 238, 0.6);
  }

  .sweepstake-stat-text {
    color: rgba(245, 243, 238, 0.8);
    font-size: 0.8rem;
  }

  .sweepstake-actions {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .sweepstake-join-btn {
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    border: none;
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
  }

  .sweepstake-view-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--marmore-ivory);
    padding: 0.5rem;
  }

  .sweepstake-entry {
    text-align: center;
    padding-top: 0.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }

  .sweepstake-entry-text {
    color: rgba(245, 243, 238, 0.6);
    font-size: 0.8rem;
  }

  .no-sweepstakes {
    text-align: center;
    padding: 2rem;
  }

  .no-sweepstakes-icon {
    width: 48px;
    height: 48px;
    color: rgba(245, 243, 238, 0.3);
    margin: 0 auto 1rem;
  }

  .no-sweepstakes-text {
    color: rgba(245, 243, 238, 0.6);
    margin: 0 0 1rem 0;
  }

  .no-sweepstakes-btn {
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
  }

  /* Sweepstakes Page */
  .sweepstakes-page {
    min-height: 100vh;
    background: var(--gradient-hero);
    padding-top: 80px;
  }

  .sweepstakes-header {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 3rem 0;
    margin-bottom: 2rem;
  }

  .sweepstakes-header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  .sweepstakes-header-text {
    text-align: center;
    margin-bottom: 3rem;
  }

  .sweepstakes-header-title {
    font-family: var(--font-titles);
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
    letter-spacing: 2px;
  }

  .sweepstakes-header-subtitle {
    font-size: 1.2rem;
    color: rgba(245, 243, 238, 0.8);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }

  .sweepstakes-header-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
  }

  .sweepstakes-stat-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
  }

  .sweepstakes-stat-card:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
  }

  .sweepstakes-stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .sweepstakes-stat-content {
    flex: 1;
  }

  .sweepstakes-stat-value {
    display: block;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--marmore-ivory);
    font-family: var(--font-titles);
  }

  .sweepstakes-stat-label {
    display: block;
    font-size: 0.9rem;
    color: rgba(245, 243, 238, 0.7);
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  .sweepstakes-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem 2rem;
  }

  .sweepstakes-top {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 2rem;
    margin-bottom: 2rem;
  }

  .sweepstakes-content {
    flex: 1;
  }

  /* Filters */
  .sweepstakes-filters {
    background: var(--gradient-card);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    min-width: 300px;
  }

  .sweepstakes-search {
    margin-bottom: 1.5rem;
  }

  .sweepstakes-search-container {
    position: relative;
    display: flex;
    align-items: center;
  }

  .sweepstakes-search-icon {
    position: absolute;
    left: 12px;
    width: 18px;
    height: 18px;
    color: rgba(245, 243, 238, 0.5);
    z-index: 1;
  }

  .sweepstakes-search-input {
    width: 100%;
    padding: 12px 16px 12px 44px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    color: var(--marmore-ivory);
    font-size: 1rem;
  }

  .sweepstakes-search-clear {
    position: absolute;
    right: 12px;
    background: none;
    border: none;
    color: rgba(245, 243, 238, 0.5);
    cursor: pointer;
    padding: 4px;
    border-radius: var(--radius-sm);
    transition: all 0.3s ease;
  }

  .sweepstakes-search-clear:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--ouro-antigo);
  }

  .sweepstakes-filter-title {
    color: var(--marmore-ivory);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
  }

  .sweepstakes-categories {
    margin-bottom: 1.5rem;
  }

  .sweepstakes-category-grid {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .sweepstakes-category-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    color: var(--marmore-ivory);
    text-align: left;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
  }

  .sweepstakes-category-btn:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .sweepstakes-category-btn.active {
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    border-color: var(--ouro-antigo);
  }

  .sweepstakes-prize-ranges {
    margin-bottom: 1.5rem;
  }

  .sweepstakes-prize-grid {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .sweepstakes-prize-badge {
    justify-content: flex-start;
    padding: 0.5rem 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .sweepstakes-prize-badge.active {
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    border-color: var(--ouro-antigo);
  }

  .sweepstakes-additional-filters {
    margin-bottom: 1rem;
  }

  .sweepstakes-checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--marmore-ivory);
    cursor: pointer;
  }

  .sweepstakes-checkbox {
    width: 16px;
    height: 16px;
    accent-color: var(--ouro-antigo);
  }

  .sweepstakes-clear-filters {
    text-align: center;
  }

  .sweepstakes-clear-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--marmore-ivory);
  }

  /* Create Sweepstake */
  .create-sweepstake-container {
    position: relative;
  }

  .create-sweepstake-btn {
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    border: none;
    font-weight: 600;
    box-shadow: var(--shadow-button);
    transition: all 0.3s ease;
  }

  .create-sweepstake-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
  }

  .create-sweepstake-options {
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 1000;
    background: var(--gradient-card);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-hero);
    min-width: 400px;
    margin-top: 0.5rem;
  }

  .create-sweepstake-options-header {
    text-align: center;
    margin-bottom: 1.5rem;
  }

  .create-sweepstake-options-header h3 {
    color: var(--marmore-ivory);
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
  }

  .create-sweepstake-options-header p {
    color: rgba(245, 243, 238, 0.7);
    font-size: 0.9rem;
    margin: 0;
  }

  .create-sweepstake-grid {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
  }

  .create-sweepstake-option {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .create-sweepstake-option:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
  }

  .create-sweepstake-option-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .create-sweepstake-option-content {
    flex: 1;
  }

  .create-sweepstake-option-title {
    color: var(--marmore-ivory);
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
  }

  .create-sweepstake-option-description {
    color: rgba(245, 243, 238, 0.7);
    font-size: 0.8rem;
    margin: 0 0 0.5rem 0;
  }

  .create-sweepstake-option-prize {
    color: var(--ouro-antigo);
    font-size: 0.8rem;
    font-weight: 600;
  }

  .create-sweepstake-option-arrow {
    width: 24px;
    height: 24px;
    color: rgba(245, 243, 238, 0.5);
    flex-shrink: 0;
  }

  .create-sweepstake-options-footer {
    display: flex;
    gap: 1rem;
    justify-content: space-between;
  }

  .create-sweepstake-cancel,
  .create-sweepstake-custom {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--marmore-ivory);
  }

  .create-sweepstake-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
  }

  /* Sweepstakes List */
  .sweepstakes-list {
    flex: 1;
  }

  .sweepstakes-list-header {
    margin-bottom: 2rem;
  }

  .sweepstakes-list-title {
    color: var(--marmore-ivory);
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
  }

  .sweepstakes-list-subtitle {
    color: rgba(245, 243, 238, 0.7);
    font-size: 0.9rem;
    margin: 0;
  }

  .sweepstakes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .sweepstake-card {
    background: var(--gradient-card);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
  }

  .sweepstake-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
  }

  .sweepstake-card.hot {
    border-color: rgba(255, 165, 0, 0.5);
    box-shadow: 0 0 20px rgba(255, 165, 0, 0.2);
  }

  .sweepstake-hot-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: linear-gradient(45deg, #ff6b35, #ff8e53);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 0 var(--radius-xl) 0 var(--radius-lg);
    font-size: 0.7rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    animation: pulse 2s infinite;
  }

  .sweepstake-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
  }

  .sweepstake-card-title-row {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
  }

  .sweepstake-card-type {
    color: var(--ouro-antigo);
  }

  .sweepstake-card-title {
    color: var(--marmore-ivory);
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
  }

  .sweepstake-card-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .sweepstake-card-stat {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }

  .sweepstake-card-stat-icon {
    width: 14px;
    height: 14px;
    color: rgba(245, 243, 238, 0.6);
  }

  .sweepstake-card-stat-text {
    color: rgba(245, 243, 238, 0.8);
    font-size: 0.8rem;
  }

  .sweepstake-card-progress {
    margin-bottom: 1rem;
  }

  .sweepstake-card-progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 0.5rem;
  }

  .sweepstake-card-progress-fill {
    height: 100%;
    background: var(--gradient-gold);
    transition: width 0.3s ease;
  }

  .sweepstake-card-progress-text {
    color: rgba(245, 243, 238, 0.6);
    font-size: 0.75rem;
  }

  .sweepstake-card-entry {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-md);
  }

  .sweepstake-card-entry-label {
    color: rgba(245, 243, 238, 0.7);
    font-size: 0.8rem;
  }

  .sweepstake-card-entry-value {
    color: var(--ouro-antigo);
    font-weight: 600;
    font-size: 0.9rem;
  }

  .sweepstake-card-actions {
    display: flex;
    gap: 0.75rem;
  }

  .sweepstake-card-join-btn {
    flex: 1;
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    border: none;
    font-weight: 600;
  }

  .sweepstake-card-view-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--marmore-ivory);
    padding: 0.5rem;
  }

  .sweepstakes-load-more {
    text-align: center;
  }

  .sweepstakes-load-more-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--marmore-ivory);
    padding: 1rem 2rem;
  }

  /* Sweepstake Room Styles */
  .sweepstake-page {
    min-height: 100vh;
    background: var(--gradient-hero);
    padding-top: 80px;
  }

  .sweepstake-room {
    min-height: calc(100vh - 80px);
  }

  .sweepstake-room-header {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2rem 0;
    margin-bottom: 2rem;
  }

  .sweepstake-room-header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  .sweepstake-room-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
  }

  .sweepstake-back-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--marmore-ivory);
  }

  .sweepstake-room-actions {
    display: flex;
    gap: 0.5rem;
  }

  .sweepstake-room-title-section {
    margin-bottom: 2rem;
  }

  .sweepstake-room-title-main {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .sweepstake-room-title {
    font-family: var(--font-titles);
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    color: var(--marmore-ivory);
    margin: 0;
  }

  .sweepstake-room-badges {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .sweepstake-type-badge {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--marmore-ivory);
  }

  .sweepstake-room-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .sweepstake-room-stat {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
  }

  .sweepstake-room-stat:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
  }

  .sweepstake-room-stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .sweepstake-room-stat-content {
    flex: 1;
  }

  .sweepstake-room-stat-value {
    display: block;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--marmore-ivory);
    font-family: var(--font-titles);
  }

  .sweepstake-room-stat-label {
    display: block;
    font-size: 0.9rem;
    color: rgba(245, 243, 238, 0.7);
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  .sweepstake-room-progress {
    margin-bottom: 1rem;
  }

  .sweepstake-room-progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
  }

  .sweepstake-room-progress-fill {
    height: 100%;
    background: var(--gradient-gold);
    transition: width 1s ease;
  }

  .sweepstake-room-progress-text {
    display: flex;
    justify-content: space-between;
    color: rgba(245, 243, 238, 0.7);
    font-size: 0.9rem;
  }

  .sweepstake-room-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem 2rem;
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 2rem;
  }

  .sweepstake-room-main {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  .sweepstake-room-sidebar {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  /* Timer Styles */
  .sweepstake-timer {
    background: var(--gradient-card);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    padding: 2rem;
    text-align: center;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
  }

  .sweepstake-timer.urgent {
    border-color: rgba(255, 165, 0, 0.5);
    box-shadow: 0 0 20px rgba(255, 165, 0, 0.2);
  }

  .sweepstake-timer.critical {
    border-color: rgba(255, 0, 0, 0.5);
    box-shadow: 0 0 20px rgba(255, 0, 0, 0.3);
    animation: pulse 1s infinite;
  }

  .sweepstake-timer-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
  }

  .sweepstake-timer-title {
    color: var(--marmore-ivory);
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
  }

  .sweepstake-timer-display {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .sweepstake-timer-unit {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    padding: 1rem;
    min-width: 80px;
    text-align: center;
  }

  .sweepstake-timer-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--ouro-antigo);
    font-family: var(--font-titles);
  }

  .sweepstake-timer-label {
    display: block;
    font-size: 0.8rem;
    color: rgba(245, 243, 238, 0.7);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-top: 0.5rem;
  }

  .sweepstake-timer-separator {
    font-size: 2rem;
    font-weight: 700;
    color: var(--ouro-antigo);
    font-family: var(--font-titles);
  }

  .sweepstake-timer-finished {
    text-align: center;
    padding: 2rem;
  }

  .sweepstake-timer-finished-title {
    color: var(--ouro-antigo);
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
  }

  .sweepstake-timer-finished-text {
    color: rgba(245, 243, 238, 0.8);
    margin: 0;
  }

  .sweepstake-timer-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    opacity: 0.3;
    pointer-events: none;
  }

  .sweepstake-timer-svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
  }

  .sweepstake-timer-ring-bg {
    fill: none;
    stroke: rgba(255, 255, 255, 0.1);
    stroke-width: 2;
  }

  .sweepstake-timer-ring-progress {
    fill: none;
    stroke: var(--ouro-antigo);
    stroke-width: 3;
    stroke-linecap: round;
    stroke-dasharray: 283;
    stroke-dashoffset: 283;
  }

  /* Info Cards */
  .sweepstake-info {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .sweepstake-info-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .sweepstake-info-card {
    background: var(--gradient-card);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    backdrop-filter: blur(10px);
  }

  .sweepstake-warning-card {
    border-color: rgba(255, 165, 0, 0.3);
    background: linear-gradient(135deg, rgba(255, 165, 0, 0.1) 0%, rgba(0, 0, 0, 0.3) 100%);
  }

  .sweepstake-info-card-header {
    padding: 1.5rem 1.5rem 0;
  }

  .sweepstake-info-card-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--marmore-ivory);
    font-size: 1.1rem;
    margin: 0;
  }

  .sweepstake-info-card-content {
    padding: 1rem 1.5rem 1.5rem;
  }

  .sweepstake-info-description {
    color: rgba(245, 243, 238, 0.8);
    line-height: 1.6;
    margin: 0 0 1.5rem 0;
  }

  .sweepstake-info-features {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .sweepstake-info-feature {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(245, 243, 238, 0.8);
    font-size: 0.9rem;
  }

  .sweepstake-info-rules {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .sweepstake-info-rule {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
  }

  .sweepstake-info-rule-number {
    width: 24px;
    height: 24px;
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 700;
    flex-shrink: 0;
  }

  .sweepstake-info-rule-text {
    color: rgba(245, 243, 238, 0.8);
    line-height: 1.5;
    flex: 1;
  }

  /* Notification System */
  .notification-container {
    position: fixed;
    top: 100px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-width: 400px;
    width: 100%;
  }

  .notification {
    background: var(--gradient-card);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: 1rem;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-hero);
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    position: relative;
    overflow: hidden;
  }

  .notification::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: currentColor;
  }

  .notification-success {
    border-left-color: #10b981;
    color: #10b981;
  }

  .notification-error {
    border-left-color: #ef4444;
    color: #ef4444;
  }

  .notification-warning {
    border-left-color: #f59e0b;
    color: #f59e0b;
  }

  .notification-info {
    border-left-color: #3b82f6;
    color: #3b82f6;
  }

  .notification-prize {
    border-left-color: var(--ouro-antigo);
    color: var(--ouro-antigo);
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(0, 0, 0, 0.3) 100%);
    animation: prize-glow 2s ease-in-out infinite alternate;
  }

  @keyframes prize-glow {
    from {
      box-shadow: var(--shadow-hero);
    }
    to {
      box-shadow: 0 20px 60px rgba(212, 175, 55, 0.3);
    }
  }

  .notification-content {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    flex: 1;
  }

  .notification-icon {
    flex-shrink: 0;
    margin-top: 0.125rem;
  }

  .notification-text {
    flex: 1;
  }

  .notification-title {
    color: var(--marmore-ivory);
    font-size: 0.9rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
  }

  .notification-message {
    color: rgba(245, 243, 238, 0.8);
    font-size: 0.8rem;
    line-height: 1.4;
    margin: 0 0 0.5rem 0;
  }

  .notification-action {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--marmore-ivory);
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .notification-action:hover {
    background: rgba(255, 255, 255, 0.2);
  }

  .notification-close {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: none;
    border: none;
    color: rgba(245, 243, 238, 0.5);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--radius-sm);
    transition: all 0.3s ease;
  }

  .notification-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--marmore-ivory);
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .notification-container {
      right: 10px;
      left: 10px;
      max-width: none;
    }
  }

  /* Live Indicator */
  .live-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(231, 76, 60, 0.2);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    color: #E74C3C;
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  .live-dot {
    width: 8px;
    height: 8px;
    background: #E74C3C;
    border-radius: 50%;
    animation: pulse-live 2s infinite;
  }

  /* Particles Effect */
  .particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
  }

  /* Glowing effects */
  .glow-gold {
    box-shadow: 0 0 30px rgba(212, 175, 55, 0.4);
  }

  .glow-maroon {
    box-shadow: 0 0 30px rgba(123, 24, 24, 0.4);
  }

  /* Glass morphism */
  .glass-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
  }

  /* Animations */
  .float {
    animation: float 3s ease-in-out infinite;
  }

  .pulse-slow {
    animation: pulse-slow 3s ease-in-out infinite;
  }

  .fade-in-up {
    animation: fadeInUp 0.8s ease-out;
  }

  .scale-in {
    animation: scaleIn 0.5s ease-out;
  }

  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(47, 47, 47, 0.2);
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(139, 111, 71, 0.5);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(139, 111, 71, 0.7);
  }
}

/* Keyframes */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-live {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes floatLeft {
  0% {
    transform: translateX(-100px) translateY(0) rotate(-10deg);
  }
  100% {
    transform: translateX(-80px) translateY(-20px) rotate(-5deg);
  }
}

@keyframes floatRight {
  0% {
    transform: translateX(100px) translateY(0) rotate(10deg);
  }
  100% {
    transform: translateX(80px) translateY(-15px) rotate(5deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }

  .hero-statue {
    width: 120px;
    height: 240px;
  }

  .stat-card {
    padding: 15px;
  }

  .stat-value {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .hero-statue {
    display: none;
  }

  .stat-card {
    padding: 12px;
  }

  .stat-value {
    font-size: 1.2rem;
  }
}
