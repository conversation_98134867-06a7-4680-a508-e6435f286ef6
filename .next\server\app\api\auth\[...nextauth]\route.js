"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/[...nextauth]/route";
exports.ids = ["app/api/auth/[...nextauth]/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5CDouglas%5CDesktop%5CChronos%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDouglas%5CDesktop%5CChronos&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5CDouglas%5CDesktop%5CChronos%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDouglas%5CDesktop%5CChronos&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Douglas_Desktop_Chronos_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/[...nextauth]/route.ts */ \"(rsc)/./src/app/api/auth/[...nextauth]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/[...nextauth]/route\",\n        pathname: \"/api/auth/[...nextauth]\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/[...nextauth]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chronos\\\\src\\\\app\\\\api\\\\auth\\\\[...nextauth]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Douglas_Desktop_Chronos_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/auth/[...nextauth]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5CDouglas%5CDesktop%5CChronos%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDouglas%5CDesktop%5CChronos&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/[...nextauth]/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/[...nextauth]/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\nconst handler = next_auth__WEBPACK_IMPORTED_MODULE_0___default()(_lib_auth__WEBPACK_IMPORTED_MODULE_1__.authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hdXRoL1suLi5uZXh0YXV0aF0vcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBZ0M7QUFDUTtBQUV4QyxNQUFNRSxVQUFVRixnREFBUUEsQ0FBQ0Msa0RBQVdBO0FBRU0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaHJvbm9zLXBsYXRmb3JtLy4vc3JjL2FwcC9hcGkvYXV0aC9bLi4ubmV4dGF1dGhdL3JvdXRlLnRzPzAwOTgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IE5leHRBdXRoIGZyb20gJ25leHQtYXV0aCdcbmltcG9ydCB7IGF1dGhPcHRpb25zIH0gZnJvbSAnQC9saWIvYXV0aCdcblxuY29uc3QgaGFuZGxlciA9IE5leHRBdXRoKGF1dGhPcHRpb25zKVxuXG5leHBvcnQgeyBoYW5kbGVyIGFzIEdFVCwgaGFuZGxlciBhcyBQT1NUIH1cbiJdLCJuYW1lcyI6WyJOZXh0QXV0aCIsImF1dGhPcHRpb25zIiwiaGFuZGxlciIsIkdFVCIsIlBPU1QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_facebook__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/providers/facebook */ \"(rsc)/./node_modules/next-auth/providers/facebook.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\n\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_lib_prisma__WEBPACK_IMPORTED_MODULE_5__.prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_facebook__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n            clientId: process.env.FACEBOOK_CLIENT_ID,\n            clientSecret: process.env.FACEBOOK_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    throw new Error(\"Email e senha s\\xe3o obrigat\\xf3rios\");\n                }\n                if (!(0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.validateEmail)(credentials.email)) {\n                    throw new Error(\"Email inv\\xe1lido\");\n                }\n                const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_5__.prisma.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    }\n                });\n                if (!user) {\n                    throw new Error(\"Usu\\xe1rio n\\xe3o encontrado\");\n                }\n                if (!user.isActive) {\n                    throw new Error(\"Conta desativada. Entre em contato com o suporte.\");\n                }\n                const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_4___default().compare(credentials.password, user.password);\n                if (!isPasswordValid) {\n                    throw new Error(\"Senha incorreta\");\n                }\n                if (!user.isVerified) {\n                    throw new Error(\"Email n\\xe3o verificado. Verifique sua caixa de entrada.\");\n                }\n                // Update last login\n                await _lib_prisma__WEBPACK_IMPORTED_MODULE_5__.prisma.user.update({\n                    where: {\n                        id: user.id\n                    },\n                    data: {\n                        lastLoginAt: new Date()\n                    }\n                });\n                return {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name || \"\",\n                    image: user.image || undefined,\n                    cpf: user.cpf,\n                    nickname: user.nickname || undefined,\n                    chronosBalance: user.chronosBalance.toString()\n                };\n            }\n        })\n    ],\n    session: {\n        strategy: \"jwt\",\n        maxAge: 30 * 24 * 60 * 60\n    },\n    jwt: {\n        maxAge: 30 * 24 * 60 * 60\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        error: \"/auth/error\"\n    },\n    callbacks: {\n        async jwt ({ token, user, trigger, session }) {\n            if (user) {\n                token.id = user.id;\n                token.cpf = user.cpf;\n                token.nickname = user.nickname;\n                token.chronosBalance = user.chronosBalance;\n            }\n            // Update token when session is updated\n            if (trigger === \"update\" && session) {\n                token.name = session.name;\n                token.email = session.email;\n                token.image = session.image;\n                token.nickname = session.nickname;\n                token.chronosBalance = session.chronosBalance;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.id;\n                session.user.cpf = token.cpf;\n                session.user.nickname = token.nickname;\n                session.user.chronosBalance = token.chronosBalance;\n            }\n            return session;\n        }\n    },\n    events: {\n        async signIn ({ user, account, profile, isNewUser }) {\n            // Log successful sign in\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_5__.prisma.auditLog.create({\n                data: {\n                    userId: user.id,\n                    action: \"USER_SIGNIN\",\n                    details: {\n                        provider: account?.provider,\n                        isNewUser\n                    }\n                }\n            });\n        },\n        async signOut ({ token }) {\n            // Log sign out\n            if (token?.id) {\n                await _lib_prisma__WEBPACK_IMPORTED_MODULE_5__.prisma.auditLog.create({\n                    data: {\n                        userId: token.id,\n                        action: \"USER_SIGNOUT\",\n                        details: {}\n                    }\n                });\n            }\n        }\n    },\n    debug: \"development\" === \"development\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2F1dGgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQ3lEO0FBQ1E7QUFDVjtBQUNJO0FBQzlCO0FBQ1E7QUFDbUI7QUFFakQsTUFBTU8sY0FBK0I7SUFDMUNDLFNBQVNSLHdFQUFhQSxDQUFDSywrQ0FBTUE7SUFDN0JJLFdBQVc7UUFDVFAsc0VBQWNBLENBQUM7WUFDYlEsVUFBVUMsUUFBUUMsR0FBRyxDQUFDQyxnQkFBZ0I7WUFDdENDLGNBQWNILFFBQVFDLEdBQUcsQ0FBQ0csb0JBQW9CO1FBQ2hEO1FBQ0FaLHdFQUFnQkEsQ0FBQztZQUNmTyxVQUFVQyxRQUFRQyxHQUFHLENBQUNJLGtCQUFrQjtZQUN4Q0YsY0FBY0gsUUFBUUMsR0FBRyxDQUFDSyxzQkFBc0I7UUFDbEQ7UUFDQWhCLDJFQUFtQkEsQ0FBQztZQUNsQmlCLE1BQU07WUFDTkMsYUFBYTtnQkFDWEMsT0FBTztvQkFBRUMsT0FBTztvQkFBU0MsTUFBTTtnQkFBUTtnQkFDdkNDLFVBQVU7b0JBQUVGLE9BQU87b0JBQVlDLE1BQU07Z0JBQVc7WUFDbEQ7WUFDQSxNQUFNRSxXQUFVTCxXQUFXO2dCQUN6QixJQUFJLENBQUNBLGFBQWFDLFNBQVMsQ0FBQ0QsYUFBYUksVUFBVTtvQkFDakQsTUFBTSxJQUFJRSxNQUFNO2dCQUNsQjtnQkFFQSxJQUFJLENBQUNuQix5REFBYUEsQ0FBQ2EsWUFBWUMsS0FBSyxHQUFHO29CQUNyQyxNQUFNLElBQUlLLE1BQU07Z0JBQ2xCO2dCQUVBLE1BQU1DLE9BQU8sTUFBTXJCLCtDQUFNQSxDQUFDcUIsSUFBSSxDQUFDQyxVQUFVLENBQUM7b0JBQ3hDQyxPQUFPO3dCQUNMUixPQUFPRCxZQUFZQyxLQUFLO29CQUMxQjtnQkFDRjtnQkFFQSxJQUFJLENBQUNNLE1BQU07b0JBQ1QsTUFBTSxJQUFJRCxNQUFNO2dCQUNsQjtnQkFFQSxJQUFJLENBQUNDLEtBQUtHLFFBQVEsRUFBRTtvQkFDbEIsTUFBTSxJQUFJSixNQUFNO2dCQUNsQjtnQkFFQSxNQUFNSyxrQkFBa0IsTUFBTTFCLHVEQUFjLENBQzFDZSxZQUFZSSxRQUFRLEVBQ3BCRyxLQUFLSCxRQUFRO2dCQUdmLElBQUksQ0FBQ08saUJBQWlCO29CQUNwQixNQUFNLElBQUlMLE1BQU07Z0JBQ2xCO2dCQUVBLElBQUksQ0FBQ0MsS0FBS00sVUFBVSxFQUFFO29CQUNwQixNQUFNLElBQUlQLE1BQU07Z0JBQ2xCO2dCQUVBLG9CQUFvQjtnQkFDcEIsTUFBTXBCLCtDQUFNQSxDQUFDcUIsSUFBSSxDQUFDTyxNQUFNLENBQUM7b0JBQ3ZCTCxPQUFPO3dCQUFFTSxJQUFJUixLQUFLUSxFQUFFO29CQUFDO29CQUNyQkMsTUFBTTt3QkFBRUMsYUFBYSxJQUFJQztvQkFBTztnQkFDbEM7Z0JBRUEsT0FBTztvQkFDTEgsSUFBSVIsS0FBS1EsRUFBRTtvQkFDWGQsT0FBT00sS0FBS04sS0FBSztvQkFDakJGLE1BQU1RLEtBQUtSLElBQUksSUFBSTtvQkFDbkJvQixPQUFPWixLQUFLWSxLQUFLLElBQUlDO29CQUNyQkMsS0FBS2QsS0FBS2MsR0FBRztvQkFDYkMsVUFBVWYsS0FBS2UsUUFBUSxJQUFJRjtvQkFDM0JHLGdCQUFnQmhCLEtBQUtnQixjQUFjLENBQUNDLFFBQVE7Z0JBQzlDO1lBQ0Y7UUFDRjtLQUNEO0lBQ0RDLFNBQVM7UUFDUEMsVUFBVTtRQUNWQyxRQUFRLEtBQUssS0FBSyxLQUFLO0lBQ3pCO0lBQ0FDLEtBQUs7UUFDSEQsUUFBUSxLQUFLLEtBQUssS0FBSztJQUN6QjtJQUNBRSxPQUFPO1FBQ0xDLFFBQVE7UUFDUkMsT0FBTztJQUNUO0lBQ0FDLFdBQVc7UUFDVCxNQUFNSixLQUFJLEVBQUVLLEtBQUssRUFBRTFCLElBQUksRUFBRTJCLE9BQU8sRUFBRVQsT0FBTyxFQUFFO1lBQ3pDLElBQUlsQixNQUFNO2dCQUNSMEIsTUFBTWxCLEVBQUUsR0FBR1IsS0FBS1EsRUFBRTtnQkFDbEJrQixNQUFNWixHQUFHLEdBQUdkLEtBQUtjLEdBQUc7Z0JBQ3BCWSxNQUFNWCxRQUFRLEdBQUdmLEtBQUtlLFFBQVE7Z0JBQzlCVyxNQUFNVixjQUFjLEdBQUdoQixLQUFLZ0IsY0FBYztZQUM1QztZQUVBLHVDQUF1QztZQUN2QyxJQUFJVyxZQUFZLFlBQVlULFNBQVM7Z0JBQ25DUSxNQUFNbEMsSUFBSSxHQUFHMEIsUUFBUTFCLElBQUk7Z0JBQ3pCa0MsTUFBTWhDLEtBQUssR0FBR3dCLFFBQVF4QixLQUFLO2dCQUMzQmdDLE1BQU1kLEtBQUssR0FBR00sUUFBUU4sS0FBSztnQkFDM0JjLE1BQU1YLFFBQVEsR0FBR0csUUFBUUgsUUFBUTtnQkFDakNXLE1BQU1WLGNBQWMsR0FBR0UsUUFBUUYsY0FBYztZQUMvQztZQUVBLE9BQU9VO1FBQ1Q7UUFDQSxNQUFNUixTQUFRLEVBQUVBLE9BQU8sRUFBRVEsS0FBSyxFQUFFO1lBQzlCLElBQUlBLE9BQU87Z0JBQ1RSLFFBQVFsQixJQUFJLENBQUNRLEVBQUUsR0FBR2tCLE1BQU1sQixFQUFFO2dCQUMxQlUsUUFBUWxCLElBQUksQ0FBQ2MsR0FBRyxHQUFHWSxNQUFNWixHQUFHO2dCQUM1QkksUUFBUWxCLElBQUksQ0FBQ2UsUUFBUSxHQUFHVyxNQUFNWCxRQUFRO2dCQUN0Q0csUUFBUWxCLElBQUksQ0FBQ2dCLGNBQWMsR0FBR1UsTUFBTVYsY0FBYztZQUNwRDtZQUVBLE9BQU9FO1FBQ1Q7SUFDRjtJQUNBVSxRQUFRO1FBQ04sTUFBTUwsUUFBTyxFQUFFdkIsSUFBSSxFQUFFNkIsT0FBTyxFQUFFQyxPQUFPLEVBQUVDLFNBQVMsRUFBRTtZQUNoRCx5QkFBeUI7WUFDekIsTUFBTXBELCtDQUFNQSxDQUFDcUQsUUFBUSxDQUFDQyxNQUFNLENBQUM7Z0JBQzNCeEIsTUFBTTtvQkFDSnlCLFFBQVFsQyxLQUFLUSxFQUFFO29CQUNmMkIsUUFBUTtvQkFDUkMsU0FBUzt3QkFDUEMsVUFBVVIsU0FBU1E7d0JBQ25CTjtvQkFDRjtnQkFDRjtZQUNGO1FBQ0Y7UUFDQSxNQUFNTyxTQUFRLEVBQUVaLEtBQUssRUFBRTtZQUNyQixlQUFlO1lBQ2YsSUFBSUEsT0FBT2xCLElBQUk7Z0JBQ2IsTUFBTTdCLCtDQUFNQSxDQUFDcUQsUUFBUSxDQUFDQyxNQUFNLENBQUM7b0JBQzNCeEIsTUFBTTt3QkFDSnlCLFFBQVFSLE1BQU1sQixFQUFFO3dCQUNoQjJCLFFBQVE7d0JBQ1JDLFNBQVMsQ0FBQztvQkFDWjtnQkFDRjtZQUNGO1FBQ0Y7SUFDRjtJQUNBRyxPQUFPdEQsa0JBQXlCO0FBQ2xDLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaHJvbm9zLXBsYXRmb3JtLy4vc3JjL2xpYi9hdXRoLnRzPzY2OTIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dEF1dGhPcHRpb25zIH0gZnJvbSAnbmV4dC1hdXRoJ1xuaW1wb3J0IHsgUHJpc21hQWRhcHRlciB9IGZyb20gJ0BuZXh0LWF1dGgvcHJpc21hLWFkYXB0ZXInXG5pbXBvcnQgQ3JlZGVudGlhbHNQcm92aWRlciBmcm9tICduZXh0LWF1dGgvcHJvdmlkZXJzL2NyZWRlbnRpYWxzJ1xuaW1wb3J0IEdvb2dsZVByb3ZpZGVyIGZyb20gJ25leHQtYXV0aC9wcm92aWRlcnMvZ29vZ2xlJ1xuaW1wb3J0IEZhY2Vib29rUHJvdmlkZXIgZnJvbSAnbmV4dC1hdXRoL3Byb3ZpZGVycy9mYWNlYm9vaydcbmltcG9ydCBiY3J5cHQgZnJvbSAnYmNyeXB0anMnXG5pbXBvcnQgeyBwcmlzbWEgfSBmcm9tICdAL2xpYi9wcmlzbWEnXG5pbXBvcnQgeyB2YWxpZGF0ZUNQRiwgdmFsaWRhdGVFbWFpbCB9IGZyb20gJ0AvbGliL3V0aWxzJ1xuXG5leHBvcnQgY29uc3QgYXV0aE9wdGlvbnM6IE5leHRBdXRoT3B0aW9ucyA9IHtcbiAgYWRhcHRlcjogUHJpc21hQWRhcHRlcihwcmlzbWEpLFxuICBwcm92aWRlcnM6IFtcbiAgICBHb29nbGVQcm92aWRlcih7XG4gICAgICBjbGllbnRJZDogcHJvY2Vzcy5lbnYuR09PR0xFX0NMSUVOVF9JRCEsXG4gICAgICBjbGllbnRTZWNyZXQ6IHByb2Nlc3MuZW52LkdPT0dMRV9DTElFTlRfU0VDUkVUISxcbiAgICB9KSxcbiAgICBGYWNlYm9va1Byb3ZpZGVyKHtcbiAgICAgIGNsaWVudElkOiBwcm9jZXNzLmVudi5GQUNFQk9PS19DTElFTlRfSUQhLFxuICAgICAgY2xpZW50U2VjcmV0OiBwcm9jZXNzLmVudi5GQUNFQk9PS19DTElFTlRfU0VDUkVUISxcbiAgICB9KSxcbiAgICBDcmVkZW50aWFsc1Byb3ZpZGVyKHtcbiAgICAgIG5hbWU6ICdjcmVkZW50aWFscycsXG4gICAgICBjcmVkZW50aWFsczoge1xuICAgICAgICBlbWFpbDogeyBsYWJlbDogJ0VtYWlsJywgdHlwZTogJ2VtYWlsJyB9LFxuICAgICAgICBwYXNzd29yZDogeyBsYWJlbDogJ1Bhc3N3b3JkJywgdHlwZTogJ3Bhc3N3b3JkJyB9LFxuICAgICAgfSxcbiAgICAgIGFzeW5jIGF1dGhvcml6ZShjcmVkZW50aWFscykge1xuICAgICAgICBpZiAoIWNyZWRlbnRpYWxzPy5lbWFpbCB8fCAhY3JlZGVudGlhbHM/LnBhc3N3b3JkKSB7XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdFbWFpbCBlIHNlbmhhIHPDo28gb2JyaWdhdMOzcmlvcycpXG4gICAgICAgIH1cblxuICAgICAgICBpZiAoIXZhbGlkYXRlRW1haWwoY3JlZGVudGlhbHMuZW1haWwpKSB7XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdFbWFpbCBpbnbDoWxpZG8nKVxuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgdXNlciA9IGF3YWl0IHByaXNtYS51c2VyLmZpbmRVbmlxdWUoe1xuICAgICAgICAgIHdoZXJlOiB7XG4gICAgICAgICAgICBlbWFpbDogY3JlZGVudGlhbHMuZW1haWwsXG4gICAgICAgICAgfSxcbiAgICAgICAgfSlcblxuICAgICAgICBpZiAoIXVzZXIpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1VzdcOhcmlvIG7Do28gZW5jb250cmFkbycpXG4gICAgICAgIH1cblxuICAgICAgICBpZiAoIXVzZXIuaXNBY3RpdmUpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0NvbnRhIGRlc2F0aXZhZGEuIEVudHJlIGVtIGNvbnRhdG8gY29tIG8gc3Vwb3J0ZS4nKVxuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgaXNQYXNzd29yZFZhbGlkID0gYXdhaXQgYmNyeXB0LmNvbXBhcmUoXG4gICAgICAgICAgY3JlZGVudGlhbHMucGFzc3dvcmQsXG4gICAgICAgICAgdXNlci5wYXNzd29yZFxuICAgICAgICApXG5cbiAgICAgICAgaWYgKCFpc1Bhc3N3b3JkVmFsaWQpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1NlbmhhIGluY29ycmV0YScpXG4gICAgICAgIH1cblxuICAgICAgICBpZiAoIXVzZXIuaXNWZXJpZmllZCkge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcignRW1haWwgbsOjbyB2ZXJpZmljYWRvLiBWZXJpZmlxdWUgc3VhIGNhaXhhIGRlIGVudHJhZGEuJylcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIFVwZGF0ZSBsYXN0IGxvZ2luXG4gICAgICAgIGF3YWl0IHByaXNtYS51c2VyLnVwZGF0ZSh7XG4gICAgICAgICAgd2hlcmU6IHsgaWQ6IHVzZXIuaWQgfSxcbiAgICAgICAgICBkYXRhOiB7IGxhc3RMb2dpbkF0OiBuZXcgRGF0ZSgpIH0sXG4gICAgICAgIH0pXG5cbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBpZDogdXNlci5pZCxcbiAgICAgICAgICBlbWFpbDogdXNlci5lbWFpbCxcbiAgICAgICAgICBuYW1lOiB1c2VyLm5hbWUgfHwgJycsXG4gICAgICAgICAgaW1hZ2U6IHVzZXIuaW1hZ2UgfHwgdW5kZWZpbmVkLFxuICAgICAgICAgIGNwZjogdXNlci5jcGYsXG4gICAgICAgICAgbmlja25hbWU6IHVzZXIubmlja25hbWUgfHwgdW5kZWZpbmVkLFxuICAgICAgICAgIGNocm9ub3NCYWxhbmNlOiB1c2VyLmNocm9ub3NCYWxhbmNlLnRvU3RyaW5nKCksXG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgfSksXG4gIF0sXG4gIHNlc3Npb246IHtcbiAgICBzdHJhdGVneTogJ2p3dCcsXG4gICAgbWF4QWdlOiAzMCAqIDI0ICogNjAgKiA2MCwgLy8gMzAgZGF5c1xuICB9LFxuICBqd3Q6IHtcbiAgICBtYXhBZ2U6IDMwICogMjQgKiA2MCAqIDYwLCAvLyAzMCBkYXlzXG4gIH0sXG4gIHBhZ2VzOiB7XG4gICAgc2lnbkluOiAnL2F1dGgvc2lnbmluJyxcbiAgICBlcnJvcjogJy9hdXRoL2Vycm9yJyxcbiAgfSxcbiAgY2FsbGJhY2tzOiB7XG4gICAgYXN5bmMgand0KHsgdG9rZW4sIHVzZXIsIHRyaWdnZXIsIHNlc3Npb24gfSkge1xuICAgICAgaWYgKHVzZXIpIHtcbiAgICAgICAgdG9rZW4uaWQgPSB1c2VyLmlkXG4gICAgICAgIHRva2VuLmNwZiA9IHVzZXIuY3BmXG4gICAgICAgIHRva2VuLm5pY2tuYW1lID0gdXNlci5uaWNrbmFtZVxuICAgICAgICB0b2tlbi5jaHJvbm9zQmFsYW5jZSA9IHVzZXIuY2hyb25vc0JhbGFuY2VcbiAgICAgIH1cblxuICAgICAgLy8gVXBkYXRlIHRva2VuIHdoZW4gc2Vzc2lvbiBpcyB1cGRhdGVkXG4gICAgICBpZiAodHJpZ2dlciA9PT0gJ3VwZGF0ZScgJiYgc2Vzc2lvbikge1xuICAgICAgICB0b2tlbi5uYW1lID0gc2Vzc2lvbi5uYW1lXG4gICAgICAgIHRva2VuLmVtYWlsID0gc2Vzc2lvbi5lbWFpbFxuICAgICAgICB0b2tlbi5pbWFnZSA9IHNlc3Npb24uaW1hZ2VcbiAgICAgICAgdG9rZW4ubmlja25hbWUgPSBzZXNzaW9uLm5pY2tuYW1lXG4gICAgICAgIHRva2VuLmNocm9ub3NCYWxhbmNlID0gc2Vzc2lvbi5jaHJvbm9zQmFsYW5jZVxuICAgICAgfVxuXG4gICAgICByZXR1cm4gdG9rZW5cbiAgICB9LFxuICAgIGFzeW5jIHNlc3Npb24oeyBzZXNzaW9uLCB0b2tlbiB9KSB7XG4gICAgICBpZiAodG9rZW4pIHtcbiAgICAgICAgc2Vzc2lvbi51c2VyLmlkID0gdG9rZW4uaWQgYXMgc3RyaW5nXG4gICAgICAgIHNlc3Npb24udXNlci5jcGYgPSB0b2tlbi5jcGYgYXMgc3RyaW5nXG4gICAgICAgIHNlc3Npb24udXNlci5uaWNrbmFtZSA9IHRva2VuLm5pY2tuYW1lIGFzIHN0cmluZ1xuICAgICAgICBzZXNzaW9uLnVzZXIuY2hyb25vc0JhbGFuY2UgPSB0b2tlbi5jaHJvbm9zQmFsYW5jZSBhcyBzdHJpbmdcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHNlc3Npb25cbiAgICB9LFxuICB9LFxuICBldmVudHM6IHtcbiAgICBhc3luYyBzaWduSW4oeyB1c2VyLCBhY2NvdW50LCBwcm9maWxlLCBpc05ld1VzZXIgfSkge1xuICAgICAgLy8gTG9nIHN1Y2Nlc3NmdWwgc2lnbiBpblxuICAgICAgYXdhaXQgcHJpc21hLmF1ZGl0TG9nLmNyZWF0ZSh7XG4gICAgICAgIGRhdGE6IHtcbiAgICAgICAgICB1c2VySWQ6IHVzZXIuaWQsXG4gICAgICAgICAgYWN0aW9uOiAnVVNFUl9TSUdOSU4nLFxuICAgICAgICAgIGRldGFpbHM6IHtcbiAgICAgICAgICAgIHByb3ZpZGVyOiBhY2NvdW50Py5wcm92aWRlcixcbiAgICAgICAgICAgIGlzTmV3VXNlcixcbiAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgICAgfSlcbiAgICB9LFxuICAgIGFzeW5jIHNpZ25PdXQoeyB0b2tlbiB9KSB7XG4gICAgICAvLyBMb2cgc2lnbiBvdXRcbiAgICAgIGlmICh0b2tlbj8uaWQpIHtcbiAgICAgICAgYXdhaXQgcHJpc21hLmF1ZGl0TG9nLmNyZWF0ZSh7XG4gICAgICAgICAgZGF0YToge1xuICAgICAgICAgICAgdXNlcklkOiB0b2tlbi5pZCBhcyBzdHJpbmcsXG4gICAgICAgICAgICBhY3Rpb246ICdVU0VSX1NJR05PVVQnLFxuICAgICAgICAgICAgZGV0YWlsczoge30sXG4gICAgICAgICAgfSxcbiAgICAgICAgfSlcbiAgICAgIH1cbiAgICB9LFxuICB9LFxuICBkZWJ1ZzogcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcsXG59XG4iXSwibmFtZXMiOlsiUHJpc21hQWRhcHRlciIsIkNyZWRlbnRpYWxzUHJvdmlkZXIiLCJHb29nbGVQcm92aWRlciIsIkZhY2Vib29rUHJvdmlkZXIiLCJiY3J5cHQiLCJwcmlzbWEiLCJ2YWxpZGF0ZUVtYWlsIiwiYXV0aE9wdGlvbnMiLCJhZGFwdGVyIiwicHJvdmlkZXJzIiwiY2xpZW50SWQiLCJwcm9jZXNzIiwiZW52IiwiR09PR0xFX0NMSUVOVF9JRCIsImNsaWVudFNlY3JldCIsIkdPT0dMRV9DTElFTlRfU0VDUkVUIiwiRkFDRUJPT0tfQ0xJRU5UX0lEIiwiRkFDRUJPT0tfQ0xJRU5UX1NFQ1JFVCIsIm5hbWUiLCJjcmVkZW50aWFscyIsImVtYWlsIiwibGFiZWwiLCJ0eXBlIiwicGFzc3dvcmQiLCJhdXRob3JpemUiLCJFcnJvciIsInVzZXIiLCJmaW5kVW5pcXVlIiwid2hlcmUiLCJpc0FjdGl2ZSIsImlzUGFzc3dvcmRWYWxpZCIsImNvbXBhcmUiLCJpc1ZlcmlmaWVkIiwidXBkYXRlIiwiaWQiLCJkYXRhIiwibGFzdExvZ2luQXQiLCJEYXRlIiwiaW1hZ2UiLCJ1bmRlZmluZWQiLCJjcGYiLCJuaWNrbmFtZSIsImNocm9ub3NCYWxhbmNlIiwidG9TdHJpbmciLCJzZXNzaW9uIiwic3RyYXRlZ3kiLCJtYXhBZ2UiLCJqd3QiLCJwYWdlcyIsInNpZ25JbiIsImVycm9yIiwiY2FsbGJhY2tzIiwidG9rZW4iLCJ0cmlnZ2VyIiwiZXZlbnRzIiwiYWNjb3VudCIsInByb2ZpbGUiLCJpc05ld1VzZXIiLCJhdWRpdExvZyIsImNyZWF0ZSIsInVzZXJJZCIsImFjdGlvbiIsImRldGFpbHMiLCJwcm92aWRlciIsInNpZ25PdXQiLCJkZWJ1ZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFFN0MsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRTtBQUVsRSxJQUFJSSxJQUF5QixFQUFjSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaHJvbm9zLXBsYXRmb3JtLy4vc3JjL2xpYi9wcmlzbWEudHM/MDFkNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCPF: () => (/* binding */ formatCPF),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatPhone: () => (/* binding */ formatPhone),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   generateInviteCode: () => (/* binding */ generateInviteCode),\n/* harmony export */   generateRandomString: () => (/* binding */ generateRandomString),\n/* harmony export */   generateSecureRandomString: () => (/* binding */ generateSecureRandomString),\n/* harmony export */   getInitials: () => (/* binding */ getInitials),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncateText: () => (/* binding */ truncateText),\n/* harmony export */   validateCPF: () => (/* binding */ validateCPF),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail),\n/* harmony export */   validatePhone: () => (/* binding */ validatePhone)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(value, currency = \"BRL\") {\n    const numValue = typeof value === \"string\" ? parseFloat(value) : value;\n    if (currency === \"BRL\") {\n        return new Intl.NumberFormat(\"pt-BR\", {\n            style: \"currency\",\n            currency: \"BRL\"\n        }).format(numValue);\n    }\n    // For Chronos currency, just format as number with 2 decimals\n    return new Intl.NumberFormat(\"pt-BR\", {\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n    }).format(numValue);\n}\nfunction formatDate(date) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    return new Intl.DateTimeFormat(\"pt-BR\", {\n        day: \"2-digit\",\n        month: \"2-digit\",\n        year: \"numeric\",\n        hour: \"2-digit\",\n        minute: \"2-digit\"\n    }).format(dateObj);\n}\nfunction formatRelativeTime(date) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);\n    if (diffInSeconds < 60) {\n        return \"agora mesmo\";\n    } else if (diffInSeconds < 3600) {\n        const minutes = Math.floor(diffInSeconds / 60);\n        return `${minutes} minuto${minutes > 1 ? \"s\" : \"\"} atrás`;\n    } else if (diffInSeconds < 86400) {\n        const hours = Math.floor(diffInSeconds / 3600);\n        return `${hours} hora${hours > 1 ? \"s\" : \"\"} atrás`;\n    } else {\n        const days = Math.floor(diffInSeconds / 86400);\n        return `${days} dia${days > 1 ? \"s\" : \"\"} atrás`;\n    }\n}\nfunction generateRandomString(length) {\n    const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n    let result = \"\";\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\nfunction validateCPF(cpf) {\n    // Remove non-numeric characters\n    const cleanCPF = cpf.replace(/\\D/g, \"\");\n    // Check if has 11 digits\n    if (cleanCPF.length !== 11) return false;\n    // Check if all digits are the same\n    if (/^(\\d)\\1{10}$/.test(cleanCPF)) return false;\n    // Validate first check digit\n    let sum = 0;\n    for(let i = 0; i < 9; i++){\n        sum += parseInt(cleanCPF.charAt(i)) * (10 - i);\n    }\n    let remainder = sum * 10 % 11;\n    if (remainder === 10 || remainder === 11) remainder = 0;\n    if (remainder !== parseInt(cleanCPF.charAt(9))) return false;\n    // Validate second check digit\n    sum = 0;\n    for(let i = 0; i < 10; i++){\n        sum += parseInt(cleanCPF.charAt(i)) * (11 - i);\n    }\n    remainder = sum * 10 % 11;\n    if (remainder === 10 || remainder === 11) remainder = 0;\n    if (remainder !== parseInt(cleanCPF.charAt(10))) return false;\n    return true;\n}\nfunction formatCPF(cpf) {\n    const cleanCPF = cpf.replace(/\\D/g, \"\");\n    return cleanCPF.replace(/(\\d{3})(\\d{3})(\\d{3})(\\d{2})/, \"$1.$2.$3-$4\");\n}\nfunction validateEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\nfunction validatePhone(phone) {\n    const cleanPhone = phone.replace(/\\D/g, \"\");\n    return cleanPhone.length >= 10 && cleanPhone.length <= 11;\n}\nfunction formatPhone(phone) {\n    const cleanPhone = phone.replace(/\\D/g, \"\");\n    if (cleanPhone.length === 10) {\n        return cleanPhone.replace(/(\\d{2})(\\d{4})(\\d{4})/, \"($1) $2-$3\");\n    } else if (cleanPhone.length === 11) {\n        return cleanPhone.replace(/(\\d{2})(\\d{5})(\\d{4})/, \"($1) $2-$3\");\n    }\n    return phone;\n}\nfunction sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\nfunction debounce(func, wait) {\n    let timeout = null;\n    return (...args)=>{\n        if (timeout) clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\nfunction getInitials(name) {\n    return name.split(\" \").map((word)=>word.charAt(0)).join(\"\").toUpperCase().slice(0, 2);\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + \"...\";\n}\nfunction generateInviteCode() {\n    const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789\";\n    let result = \"\";\n    for(let i = 0; i < 8; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\nfunction generateSecureRandomString(length) {\n    const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n    let result = \"\";\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/clsx","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/@next-auth","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5CDouglas%5CDesktop%5CChronos%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDouglas%5CDesktop%5CChronos&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();