// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model for authentication and profile
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  emailVerified DateTime?
  name          String?
  cpf           String    @unique
  phone         String?
  image         String?
  password      String
  isActive      Boolean   @default(true)
  isVerified    Boolean   @default(false)
  twoFactorEnabled Boolean @default(false)
  
  // Chronos balance
  chronosBalance Decimal @default(0) @db.Decimal(10, 2)
  
  // Profile data
  nickname      String?   @unique
  birthDate     DateTime?
  address       String?
  city          String?
  state         String?
  zipCode       String?
  
  // Banking info for withdrawals
  bankName      String?
  bankAccount   String?
  bankAgency    String?
  pixKey        String?
  
  // Referral system
  referralCode  String?   @unique
  referredBy    String?
  
  // Timestamps
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  lastLoginAt   DateTime?
  
  // Relations
  accounts      Account[]
  sessions      Session[]
  transactions  Transaction[]
  roomParticipations RoomParticipation[]
  drawResults   DrawResult[]
  auditLogs     AuditLog[]
  chatMessages  ChatMessage[]
  referrals     User[]    @relation("UserReferrals")
  referrer      User?     @relation("UserReferrals", fields: [referredBy], references: [id])
  
  @@map("users")
}

// NextAuth Account model
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

// NextAuth Session model
model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

// Verification tokens for email verification
model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// Transaction model for Chronos currency
model Transaction {
  id          String            @id @default(cuid())
  userId      String
  type        TransactionType
  amount      Decimal           @db.Decimal(10, 2)
  status      TransactionStatus @default(PENDING)
  description String?
  
  // Payment gateway data
  gatewayId       String?
  gatewayType     PaymentGateway?
  gatewayResponse Json?
  
  // For P2P transfers
  fromUserId      String?
  toUserId        String?
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  user        User     @relation(fields: [userId], references: [id])
  
  @@map("transactions")
}

// Room model for sweepstakes/draws
model Room {
  id              String     @id @default(cuid())
  name            String?
  type            RoomType
  betAmount       Decimal    @db.Decimal(10, 2)
  maxParticipants Int
  minParticipants Int        @default(2)
  houseFee        Decimal    @default(0.50) @db.Decimal(10, 2)
  
  status          RoomStatus @default(WAITING)
  isPrivate       Boolean    @default(false)
  inviteCode      String?    @unique
  
  // Timing
  startTime       DateTime?
  endTime         DateTime?
  drawTime        DateTime?
  
  // Prize calculation
  totalPrize      Decimal?   @db.Decimal(10, 2)
  
  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt
  
  // Relations
  participants    RoomParticipation[]
  drawResult      DrawResult?
  chatMessages    ChatMessage[]
  auditLogs       AuditLog[]
  
  @@map("rooms")
}

// Room participation
model RoomParticipation {
  id       String @id @default(cuid())
  userId   String
  roomId   String
  position Int?   // Position in the draw (1st, 2nd, etc.)
  
  joinedAt DateTime @default(now())
  
  user     User   @relation(fields: [userId], references: [id])
  room     Room   @relation(fields: [roomId], references: [id])
  
  @@unique([userId, roomId])
  @@map("room_participations")
}

// Draw results with cryptographic proof
model DrawResult {
  id              String   @id @default(cuid())
  roomId          String   @unique
  winnerId        String
  
  // Cryptographic data
  seed            String   // Obfuscated seed for transparency
  seedHash        String   // SHA-256 hash of the original seed
  randomValue     String   // Generated random value
  timestamp       DateTime
  
  // Participants snapshot
  participantsSnapshot Json
  
  // Prize distribution
  prizeAmount     Decimal  @db.Decimal(10, 2)
  houseFeeAmount  Decimal  @db.Decimal(10, 2)
  
  createdAt       DateTime @default(now())
  
  room            Room     @relation(fields: [roomId], references: [id])
  winner          User     @relation(fields: [winnerId], references: [id])
  
  @@map("draw_results")
}

// Chat messages in rooms
model ChatMessage {
  id        String   @id @default(cuid())
  roomId    String
  userId    String
  message   String
  timestamp DateTime @default(now())
  
  room      Room     @relation(fields: [roomId], references: [id])
  user      User     @relation(fields: [userId], references: [id])
  
  @@map("chat_messages")
}

// Audit logs for transparency
model AuditLog {
  id          String    @id @default(cuid())
  userId      String?
  roomId      String?
  action      String
  details     Json?
  ipAddress   String?
  userAgent   String?
  timestamp   DateTime  @default(now())
  
  // Cryptographic signature
  signature   String?
  
  user        User?     @relation(fields: [userId], references: [id])
  room        Room?     @relation(fields: [roomId], references: [id])
  
  @@map("audit_logs")
}

// Enums
enum TransactionType {
  DEPOSIT
  WITHDRAWAL
  ROOM_ENTRY
  PRIZE_WIN
  TRANSFER_SENT
  TRANSFER_RECEIVED
  REFERRAL_BONUS
  HOUSE_FEE
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
}

enum PaymentGateway {
  STRIPE
  PAYPAL
  PIX
  BANK_TRANSFER
}

enum RoomType {
  DEMO
  INDIVIDUAL
  X1
  X1_GROUP
  MONTHLY_BATTLE
}

enum RoomStatus {
  WAITING
  STARTING
  IN_PROGRESS
  COMPLETED
  CANCELLED
}
