# Database
DATABASE_URL="postgresql://username:password@localhost:5432/chronos_db"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# Stripe
STRIPE_PUBLISHABLE_KEY="pk_test_..."
STRIPE_SECRET_KEY="sk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

# PayPal
PAYPAL_CLIENT_ID="your-paypal-client-id"
PAYPAL_CLIENT_SECRET="your-paypal-client-secret"
PAYPAL_ENVIRONMENT="sandbox" # or "production"

# Pix (Gerencianet)
GERENCIANET_CLIENT_ID="your-gerencianet-client-id"
GERENCIANET_CLIENT_SECRET="your-gerencianet-client-secret"
GERENCIANET_ENVIRONMENT="sandbox" # or "production"

# Redis (for caching and pub/sub)
REDIS_URL="redis://localhost:6379"

# Email (for notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# AWS S3 (for file storage)
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="chronos-platform-bucket"

# Socket.IO
SOCKET_IO_SECRET="your-socket-secret"

# Crypto Seeds
CRYPTO_SEED_SECRET="your-crypto-seed-secret"

# App Settings
APP_NAME="Chronos Platform"
APP_URL="http://localhost:3000"
SUPPORT_EMAIL="<EMAIL>"
